import {getWxJsapiSignature} from "@/common/request/order";

var jweixin = require('jweixin-module');
export default {// 检测api是否可用
    checkJsApi: function() {
        console.log('checkJsApi', this.isWechat())
        jweixin.error(function(res){
            console.log('err ', res)
        });
        jweixin.ready(function(){
            console.log('111')
        });
        jweixin.checkJsApi({
            jsApiList: ['chooseImage'], // 需要检测的 JS 接口列表
            success: function(res) {
                console.log('checkJsApi', res)
                // 以键值对的形式返回，可用的 api 值true，不可用为false
            },
            fail: function (res) {
                console.log('checkJsApi', res)
            },
            complete: function (res) {
                console.log('checkJsApi', res)
            }
        })
    },
    //判断是否在微信中
    isWechat: function() {
        var ua = window.navigator.userAgent.toLowerCase();
        console.log(ua)
        if (ua.match(/micromessenger/i) == 'micromessenger') {
            console.log('是微信客户端')
            return true;
        } else {
            console.log('不是微信客户端')
            return false;
        }
    },
    initJssdk:function(callback){
        var uri = encodeURIComponent(window.location.href.split('#')[0]);//获取当前url然后传递给后台获取授权和签名信息
        console.log('uri', uri)
        getWxJsapiSignature({url: uri}).then(res=> {
            const data = res.data;
            jweixin.config({
                debug: true,
                appId: data.appId,
                timestamp: data.timestamp,
                nonceStr: data.nonceStr,
                signature: data.signature,
                jsApiList: [//这里是需要用到的接口名称
                    'chooseWXPay',//微信支付
                ]
            });
            if (callback) {
                callback(res.data);
            }
        }).catch(res=> {
            console.log(res);
        })
    },
    chooseImage:function(callback){//选择图片
        if (!this.isWechat()) {
            //console.log('不是微信客户端')
            return;
        }
        //console.log(data);
        this.initJssdk(function(res) {
            jweixin.ready(function() {
                jweixin.chooseImage({
                    count:1,
                    sizeType:['compressed'],
                    sourceType:['album'],
                    success:function(rs){
                        callback(rs)
                    }
                })
            });
        });
    },
    //微信支付
    wxPay: function(data,callback) {
        if (!this.isWechat()) {
            console.log('不是微信客户端')
            return;
        }
        this.initJssdk(function(res) {
            console.log('initJssdk', res)
            jweixin.ready(function() {
                console.log('ready')
                //"appId": "@Model.appid", //公众号名称，由商户传入
                //             "timeStamp": "@Model.Timestamp", //时间戳
                //             "nonceStr": "@Model.nonce_str", //随机串
                //             "package": "@Html.Raw(Model.prepay_id)",//扩展包
                //             "signType": "MD5", //微信签名方式:MD5
                //             "paySign": "@Model.PaySign" //微信签名
                alert('packageValue:' + JSON.stringify(data))
                jweixin.chooseWXPay({
                    timestamp: data.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
                    nonceStr: data.nonceStr, // 支付签名随机串，不长于 32 位
                    package: data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
                    signType: data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                    paySign: data.paySign, // 支付签名
                    success: function (res) {
                        console.log(res);
                        callback(res)
                    },
                    fail:function(res){
                        alert('fail:' + JSON.stringify(res));
                        callback(res)
                    },
                    // complete:function(res){
                    //     console.log(res)
                    // }
                });
            });
        });
    }
}