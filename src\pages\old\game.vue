<template>
  <view class="content">
    <view class="scroll-box">
      <uni-card isShadow :note="game.priceDay" title="1" :showFooter="canBuy(game)" titleClass="uni-card-title">
        <template v-slot:header>
          <view class="com-card-title">
            <view class="title-left">
              <view>{{ game.seriesName }}</view>
            </view>
            <view class="title-right">

            </view>
          </view>
        </template>
        <view class="com-card-content">
          <view class="series-content">
            <view class="series-img" align="center">
              <img :src="game.image|productImg" mode="aspectFill"/>
            </view>
            <view class="series-memo">
              {{ game.seriesPromotion }}
            </view>
          </view>

          <view class="series-history-box m-t">
            <view class="jz-button" @click.stop="goto('/series', {id: game.promoSeriesId})">
              <text class="yhl-icon">
                <image :src="'/admin/sys-file/fb-mp/hong.gif'|productImg" mode="aspectFit"/>
              </text>
              <text class="jz-button-text">查看近期成绩</text>
            </view>
          </view>
          <view class="series-price-box">
            <view class="series-price-item" v-if="game.priceDay">
              <text class="ylh-icon icon-taojinbi color-red">{{ game.priceDay }}</text>
              <text style="padding-left:4rpx">单次</text>
            </view>
            <view class="series-price-item" v-if="game.priceWeek">
              <text class="ylh-icon icon-taojinbi color-red">{{ game.priceWeek }}</text>
              <text style="padding-left:4rpx">包周</text>
            </view>
            <view class="series-price-item" v-if="game.priceMonth">
              <text class="ylh-icon icon-taojinbi color-red">{{ game.priceMonth }}</text>
              <text style="padding-left:4rpx">包月</text>
            </view>
          </view>
          <view class="last-game-box">
            <view class="ylh-icon icon-RacingFlag"></view>
            <view class="game-name">
              [{{ game.gameName }}]
            </view>
            <view class="game-home-away">
              <text class="game-home">{{ game.home }}</text>
              <text class="game-vs">VS</text>
              <text class="game-away">{{ game.away }}</text>
            </view>
            <view class="play-time">{{ game.playTime | date('MM-dd hh:mm') }}</view>
          </view>
          <view class="game-remark" v-if="game.gameRemark">
            <text>推介：</text>
            <rich-text :nodes="game.gameRemark"></rich-text>
          </view>
        </view>
        <view class="discount-buy-box" v-if="(game.priceWeek || game.priceMonth)">
          <view class="jz-button jz-button-week" v-if="game.priceWeek" @click.stop="buy(game, '包周')">
            <text class="ylh-icon icon-coin"></text> {{game.priceWeek}}包周
          </view>
          <view class="jz-button jz-button-month" v-if="game.priceMonth" @click.stop="buy(game, '包月')">
            <text class="ylh-icon icon-coin"></text> {{game.priceMonth}}包月
          </view>
        </view>
        <template v-slot:footer v-if="canBuy(game)">
          <view class="com-card-bottom">
            <view class="jz-button-background" @click.stop="buy(game)">
              <img :src="'/admin/sys-file/football-g/fb-e-dingyue.gif'|productImg" mode="aspectFill"/>
            </view>
          </view>
        </template>
      </uni-card>
      <view class="charge-box">
        <view class="jz-button-background charge-button" @click.stop="gotoName('coinCharge')">
          <view class="jz-button1 gradient full-border">
            <text class="ylh-icon icon-taojinbi"></text>
            <text class="jz-button-text">点击充值</text>
          </view>
        </view>
        <!--                <view class="jz-button-background" @click="gotoName('coinCharge')">-->
        <!--                    <img src="https://oss.yj-star.com/admin/sys-file/football-g/fb-e-chongzhi.gif" mode="aspectFill" />-->
        <!--                </view>-->
      </view>
    </view>
    <view class="ad-box">
      <img :src="$website.ossDomain + '/admin/sys-file/football-g/fb-' + $website.siteName + '-ad-game.jpg'"
           mode="aspectFill"/>
    </view>
  </view>

</template>

<script>
import uniCard from '@/components/uni-card'
import {
  getGameItemBeforePlayTime,
  getGameResultText, getHistoryGameList
} from "@/common/request/promo-series";
import {isEmpty} from "@/util/validate";
import promoBuy from "@/pages/home/<USER>/promo-buy";

export default {
  components: {uniCard},
  mixins: [promoBuy],
  data() {
    return {
      rawPay: true,
      results: [],
      gameId: '',
      promoSeriesId: '',
      game: {
        gameRemark: 'x'
      },
      expired: {
        d: undefined,
        h: undefined,
        m: undefined,
        s: undefined
      },
      winHistory: {
        times: 10,
        winTimes: 0,
      },
      historyPromoList: [],
      loadStatus: 'more',
      loadFlag: false,
      pageInfo: {
        current: 0,
        size: 8,
      },

    }
  },
  onLoad() {
    console.log('onload')
  },
  onShow() {
    let options = this.getParams();
    this.gameId = options.id;
    this.promoSeriesId = options.promoSeriesId;
    console.log(this.gameId, this.promoSeriesId)
    if (this.gameId) {
      this.getGameItem();
    }
    if (this.promoSeriesId) {
      this.getResults();
      this.getHistoryGameList();
    }
  },
  methods: {
    canBuy(item) {
      const gamePromo = item;

      //没有赛事不能买
      if (gamePromo == null) {
        return false;
      }
      //未购买过
      const isNotBuy = isEmpty(item.buyTime);

      // if(item.type === '篮球' && isNotBuy) {
      //     return true;
      // }

      //未到开始时间
      let gameNotStart = false;
      if (gamePromo.playTime) {
        let now = new Date();
        //console.log('canBuy', gamePromo.playTime)
        let playTime = new Date(gamePromo.playTime.replace(/-/g, "/"));
        playTime = new Date(playTime.setHours(playTime.getHours() + 2))
        gameNotStart = playTime > now;
      }

      //已出料 && 未到开始时间 && 未发布结果 && 未购买过
      console.log(item.hasPromotion, gameNotStart, isEmpty(gamePromo.result), isNotBuy)
      return item.hasPromotion && gameNotStart && isEmpty(gamePromo.result) && isNotBuy
    },
    getGameItem() {
      const me = this;
      getGameItemBeforePlayTime({id: this.gameId}).then(res => {
        me.game = res.data;
        me.calcTime(me.game.playTime);
      }).catch(res => {
      });
    },
    getResults() {
      const me = this;
      getGameResultText(me.promoSeriesId).then(res => {
        if (res.data) {
          me.results = res.data.split('');
          let times = 0;
          me.winHistory.times = me.results.length;
          for (let i = 0; i < me.results.length; i++) {
            if (this.results[i] === 'W') {
              times++;
              me.winHistory.winTimes++;
            }
          }
          if (times > 0) {
            me.winTimes = times + '连胜';
          }
        }
      }).catch(res => {
      });
    },
    getHistoryGameList(nextPage) {
      if (this.loadFlag) {
        return;
      }
      this.loadFlag = true;
      if (nextPage) {
        this.pageInfo.current += 1;
      } else {
        this.loadStatus = 'more';
        this.historyPromoList = [];
        this.pageInfo.current = 1;
      }
      if (this.loadStatus !== 'more') {
        this.loadFlag = false;
        return;
      }
      this.pageInfo.promoSeriesId = this.promoSeriesId;
      uni.showLoading();
      getHistoryGameList(this.pageInfo).then(res => {
        this.loadFlag = false;
        const data = res.data.records;
        if (!isEmpty(data)) {
          this.historyPromoList = this.historyPromoList.concat(data);
        }
        if (!data || data.length < this.pageInfo.size) {
          this.loadStatus = 'noMore'
        }
        uni.hideLoading();
      }).catch(res => {
        this.loadFlag = false;
        uni.hideLoading()
      });
    },
    resultClass(result) {
      switch (result) {
        default:
        case 'D':
          return 'color-green';
        case 'W':
          return 'color-red';
        case 'L':
          return 'color-black';
      }
    },
    calcTime(d) {
      const now = new Date().getTime();
      const end = new Date(d).getTime();
      const leftTime = end - now;
      if (leftTime >= 0) {
        this.expired.d = Math.floor(leftTime / 1000 / 60 / 60 / 24);
        this.expired.h = Math.floor(leftTime / 1000 / 60 / 60 % 24);
        this.expired.m = Math.floor(leftTime / 1000 / 60 % 60);
        this.expired.s = Math.floor(leftTime / 1000 % 60);
      }
    }
  }
}
</script>

<style lang="scss">
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-box {
  flex: 1;
  background-color: $page-color-base;
  overflow-y: scroll;
}
.discount-buy-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 20rpx 60rpx;
  align-items: center;
  .jz-button {
    width: 40%;
  }
  .jz-button-week {
    background-color: #e7234e;
    padding: 16rpx 8rpx;
  }
  .jz-button-month {
    background-color: #F79A07;
    padding: 16rpx 8rpx;
  }
}
.ad-box {
  //height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
  }
}

.bg-white {
  background-color: #FFFFFF;
}

.jz-row {
  width: 100%;
}

.uni-card-title {
  background-color: #e7234e;
}

.com-card-title {
  flex: 1;
  color: #FFFFFF;
  font-weight: 700;
  font-size: $font-lg + 6rpx;
}

.com-card-content {
  display: flex;
  flex-direction: column;
  padding: 16rpx;

  .series-content {
    display: flex;
    flex-direction: row;

    .series-img {
      flex: 1;

      img {
        width: 100%;
      }
    }

    .series-memo {
      flex: 1;
      font-size: $font-sm;
      color: $font-color-light;
      padding: 12rpx 20rpx;
    }
  }

  .series-history-box {
    .jz-button {
      background-color: #6e727b;
      padding: 12rpx 0;
      align-items: center;
      justify-content: center;

      .yhl-icon {
        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .jz-button-text {
        padding-left: 4rpx;
      }
    }
  }

  .series-price-box {
    display: flex;
    flex-direction: row;
    font-size: $font-base;
    font-weight: 700;
    padding: 16rpx 0;
    .series-price-item {
      width: 100%;
      display: flex;
    }
  }

  .last-game-box {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-lg;
    padding: 16rpx 0;

    .ylh-icon {
      color: #e7234e;
      padding-right: 8rpx;
      font-size: $font-lg + 8rpx;
    }

    .game-name {

    }

    .game-home-away {
      padding-left: 10rpx;
      color: #4a4c55;

      .game-home {
        font-weight: 700;
      }

      .game-away {
        font-weight: 700;
      }

      .game-vs {
        padding: 0 4rpx;
        font-size: $font-sm;
      }
    }

    .play-time {
      padding-left: 10rpx;
      color: #888888;
      font-size: $font-base;
    }
  }

  .game-remark {
    display: flex;
    flex-direction: row;
  }

  .color-red {
    color: #e7234e;
  }
}

.com-card-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
}

.jz-button-background {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.charge-box {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  margin-top: 34rpx;
  padding: 20rpx 100rpx;
  .charge-button {
    .jz-button1 {
      color: #ffffff;
      width: 100%;
      font-size: $font-base;
      border-radius: 8rpx;
      text-align: center;
      padding: 16rpx;
      .ylh-icon {
        font-size: $font-lg + 18rpx;
      }
    }
    .gradient {
      /* 设置容器尺寸 - 原理1 */
      //width: 400px;
      //height: 400px;
      /* 背景渐变色 - 原理2 */
      background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
      /* 背景尺寸 - 原理3 */
      background-size: 600% 600%;
      /* 循环动画 - 原理4 */
      animation: gradientBG 5s ease infinite;
    }

    /* 动画，控制背景 background-position */
    @keyframes gradientBG {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
    .jz-button-text {
      font-size: $font-lg + 12rpx;
    }
  }
}
</style>
