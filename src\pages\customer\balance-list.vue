<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getData(true)">
            <!-- 查询状态切换 -->
            <list-tab ref="consumeListTab" :tabCurrent="tabCurrent" :tabs="tabs" @click="reload"></list-tab>
            <view v-for="(item, index) in pageInfo.list" :key="index">
                <uni-card isShadow :note="item.remark">
                    <template v-slot:header>
                        <view class="com-card-title">
                            <view class="game-name"><text class="game-name-text">{{item.changeType}}</text></view>
                            <text class="play-time">{{item.createdTime | date('yyyy-MM-dd hh:mm:ss')}}</text>
                        </view>
                    </template>
                    <view class="com-card-content">
                        <view class="jz-row">
                            <view class="game-left">变动金币：</view>
                            <view class="game-right">{{item.changeCoin|xNumber(0)}}</view>
                        </view>
                        <view class="jz-row">
                            <view class="game-left">剩余金币：</view>
                            <view class="game-right">{{(item.remainCoin+(item.backupRemainCoin||0))|number(0)}}</view>
                        </view>
                    </view>
                </uni-card>
            </view>
            <uni-load-more v-if="pageInfo.params.size > 0 && pageInfo.list.length > 0"
                           :status="pageInfo.loadStatus"></uni-load-more>
            <jz-empty v-if="!pageInfo.list || pageInfo.list.length === 0" content="暂无数据"></jz-empty>
        </scroll-view>
        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniCard from '@/components/uni-card'
import listTab from '@/components/list-tab'
import uniFab from '@/components/uni-fab'
import jzEmpty from '@/components/jz-empty';
import uniLoadMore from '@/components/uni-load-more'
import {getBalanceHistory} from '@/common/request/customer'
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";

export default {
    components: {uniCard, listTab, uniFab, jzEmpty, uniLoadMore},
    data() {
        return {
            tabCurrent: '全部',
            tabs: ['全部', '充值', '支出'],
            pageInfo: {
                list: [],
                loadStatus: 'more',
                params: {
                    current: 0,
                    size: 8,
                },
            }
        }
    },
    methods: {
        reload(tab) {
            this.getData(false, tab)
        },
        getData(nextPage, changeType) {
            bindScrollPageInfo(this.pageInfo, nextPage).then(pageInfo => {
                this.pageInfo = pageInfo;
                if(changeType && changeType !== '全部') {
                    this.pageInfo.params = Object.assign(this.pageInfo.params, {changeType: changeType})
                }
                else {
                    this.pageInfo.params.changeType = null;
                }
                getBalanceHistory(this.pageInfo.params).then(res=> {
                    const data = res.data;
                    this.pageInfo = bindPageInfo(this.pageInfo, data)
                }).catch(res=>{});
            }).catch(()=> {})
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
    padding: 20rpx 20rpx 0 20rpx;
}
.com-card-title {
    flex: 1;
    display: flex;
    justify-content: space-between;
    .game-name{
        display: flex;
        flex: 2;
        align-items: center;
        .game-name-text {
            flex: 1;
            font-size: $uni-font-size-lg;
            color: #333;
            font-weight: 600;
        }
    }
    .promo-series-name {
        min-width: 140rpx;
    }
    .play-time {
        flex: auto;
        text-align: right;
        color: $uni-text-color-grey;
        font-size: $uni-font-size-sm;
    }
}
.jz-row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.com-card-content {
    width: 100%;
    .game-left {
        flex: auto;
        display: flex;
        justify-content: space-between;
        color: $font-color-light;
        align-items: center;
        font-size: $font-lg - 4rpx;
        padding: 12rpx 12rpx 12rpx 24rpx;
    }
    .game-right {
        flex: auto;
        padding-left: 20rpx;
        padding-right: 20rpx;
        text-align: right;
    }
    .game-result-icon {
        display:flex;
        justify-content:center;
        border:2rpx solid red;
        border-radius:50%;
        width:50rpx;
        height:50rpx;
        line-height:44rpx;
        font-size: 28rpx;
        font-weight: 600;
        color:red;
    }
}
.com-card-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .com-card-bottom-left {
        .promo-date {
            //color: $uni-text-color-grey;
            //font-size: $uni-font-size-sm;
        }
    }
    .com-card-bottom-right {
        .com-button {

        }
    }
}
</style>