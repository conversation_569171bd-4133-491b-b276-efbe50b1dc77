<template>
    <view class="memo-box">
        <view class="memo" v-if="items && items.length > 0">
            <view v-for="(item, index) in items" :index="index" :key="index" v-bind:style="style">
                {{item.value}}
            </view>
        </view>
    </view>
</template>

<script>

import {getDictListByType, getDictListByTypeAndTenantId} from "@/common/request/admin";

export default {
    name: "comDictMemo",
    props: {
        dictType: '',
        tenantId: undefined,
        styleObject: {
            color: 'red',
            fontSize: '13px'
        }
    },
    data() {
        return {
            items: [],
            style: {
                color: '#999999',
                fontSize: '14px',
                lineHeight: '22px',
            }
        }
    },
    mounted() {
        this.loadData();
    },
    methods: {
        loadData() {
            const me = this;
            if(me.dictType) {
                const funcDict = this.tenantId ? getDictListByTypeAndTenantId : getDictListByType;
                funcDict(me.dictType, this.tenantId).then((res) => {
                    me.items = res.data;
                    console.log(me.items)
                }).catch(res => {});
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.memo-box {
    padding: 20rpx;
    .memo {
        display: flex;
        flex-direction: column;
    }
}
</style>
