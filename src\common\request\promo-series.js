import {get, post, del} from "@/common/request/jz-request";
import {config} from "@/common/jz-constant";

export const getSimpleAllList = () => get('/gamepromoseries/simple/all')
export const getGameItem = params => get('/gamepromo/item', params)
export const getGameItemBeforePlayTime = params => get('/gamepromo/item/beforePlayTime', params)
export const getTodayGameList = params => get('/gamepromo/today', params)
export const getTodayAiGameList = params => get('/gamepromo/ai/today', params)
export const getHistoryGameList = params => get('/gamepromo/history', params)
export const getSeriesId = id => get('/gamepromoseries/item/' + id)
export const getGameResultList = id => get('/gamepromoseries/result/list/' + id, {count: 15})
export const getGameResultText = id => get('/gamepromoseries/result/text/' + id)
export const getAiCount = () => get('/gamepromoseries/ai/count')


export const getSeriesGame = params => get('/gamepromoseries/games', params)



export const followGame = params => post('/gamefollow/save', params)
export const unFollowGame = id => del('/gamefollow/' + id)