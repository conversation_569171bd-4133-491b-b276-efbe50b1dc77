    #server {
    #    listen       80;
    #    server_name  fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com;
	#	rewrite ^(.*)$  https://$host$1 permanent;
    #}
	
	server {
		listen	80;
		server_name f.qyqy268.com fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com;
		location / {
			root /etc/nginx/beian/qyqy268com/;
        }
	}
	server {
        listen       443 http2 ssl;
        server_name  f.qyqy268.com;
        ssl_certificate      qyqy268.com.cer;
        ssl_certificate_key  qyqy268.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;

        location /wx5a6796791b1886d3 {
            add_header Cache-Control "max-age=0";
            rewrite ^(.*)$  https://fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com/wx5a6796791b1886d3/ permanent;
        }

        location ^~/hwechat/ {
            add_header Cache-Control "max-age=0";
            rewrite ^(.*)$  https://fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com/hwechat/wx5a6796791b1886d3/ permanent;
        }

		location ^~/mgr/ {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com/mgr/ permanent;
		}

		location ~* ^/mgr/ {
			root /home/<USER>/A/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /mgr/$uri $uri /mgr/index.html;
        }

        location / {
            add_header Cache-Control "max-age=0";
            rewrite ^(.*)$  https://fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com/ permanent;
        }
    }
	server {
        listen       443 http2 ssl;
        server_name  fg3gevcxjb3zkskraisvnthtlxzzpmgbjo7au8eypodxpjt7gvjegx4rddzdilp.qyqy268.com;
        ssl_certificate      qyqy268.com.cer;
        ssl_certificate_key  qyqy268.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css application/javascript;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
		#root /home/<USER>/A/jstar-ui/;
		#root /home/<USER>/ui/front/win08/;
		
		
		
		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}
		
		location /wx5a6796791b1886d3 {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://$host/hwechat$1 permanent;
		}
		
		location ^~/h5/ {
			root /home/<USER>/F/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /h5/wx57cb8affab0f7a94/$uri $uri /h5/wx57cb8affab0f7a94/index.html;
        }
		
		location ^~/hwechat/ {
			root /home/<USER>/F/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /hwechat/wx5a6796791b1886d3/$uri $uri /hwechat/wx5a6796791b1886d3/index.html;
        }
		
        location ~* ^/(football|football-g|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_f;
        }
		
		#location ~* /mgr/\.(gif|jpg|png|css|js|ttf|woff) {
		#	root /home/<USER>/A/;
		#	proxy_read_timeout 300;
		#	#try_files /mgr/$uri $uri 404;
		#}
		
		location ~* ^/mgr/ {
			root /home/<USER>/A/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /mgr/$uri $uri /mgr/index.html;
        }
		
		location / {
			root /etc/nginx/beian/qyqy268com/;
        }

        #error_page   500 502 503 504  /50x.html;
        #location = /50x.html {
        #    root   html;
        #}
    }