import {getStore, setStore, removeStore} from '@/util/store'
import {isURL, isEmpty} from '@/util/validate'
import {deepClone, encryption} from '@/util/util'
import {auth} from '@/common/request/auth'
import {
  getMyWallet,
  getMyWalletByUserId,
  getMyWxAccountFansInfo,
  getWxAccountFansInfoByUserId
} from '@/common/request/customer'
import { common } from './common'

const user = {
  state: {
    wallet: getStore({
      name: 'wallet'
    }) || {},
    userInfo: getStore({
      name: 'userInfo'
    }) || {},
    accountFans: getStore({
      name: 'accountFans'
    }) || {},
    menu: getStore({
      name: 'menu'
    }) || [],
    expires_in: getStore({
      name: 'expires_in'
    }) || '',
    access_token: getStore({
      name: 'access_token'
    }) || '',
    refresh_token: getStore({
      name: 'refresh_token'
    }) || '',
    other_customer: getStore({
      name: 'other_customer'
    }) || '',
    mpCode: '',
    nextRoute: getStore({
      name: 'nextRoute'
    }) || {},
    origin_user_id: getStore({
      name: 'origin_user_id'
    }) || '',
    origin_tenant_id: getStore({
      name: 'origin_tenant_id'
    }) || '',
  },
  actions: {
    isLogin({ dispatch, state }, to) {
      if (to && to.isGuest) {
        console.log('guest')
        return Promise.resolve();
      }
      return new Promise((resolve, reject) => {
        //debugger
        const userInfo = state.userInfo;
        if(!isEmpty(userInfo) && !isEmpty(state.access_token) && state.expires_in >= new Date().getTime()) {
          resolve()
        }
        else {
          dispatch('removeUserCache');
          reject();
        }
      });
    },
    getWallet({ dispatch, commit, state }) {
      return new Promise((resolve, reject) => {
        getMyWallet().then(res => {
          const d = res.data;
          if(d && d.coin >= 0) {
            resolve(d)
          } else {
            reject();
          }
        }).catch(res => {
          reject();
        });
      })
    },
    getAccountFans({ dispatch, commit, state }) {
      return new Promise((resolve, reject) => {
        if(isEmpty(state.userInfo) || isEmpty(state.userInfo.wxOpenid)) {
          reject();
        }
        getMyWxAccountFansInfo().then(res => {
          commit('SET_ACCOUNT_FANS', res.data);
          resolve(state.accountFans)
        }).catch(res => {
          console.log(res);
          reject();
        });
        // if(!isEmpty(state.accountFans)) {
        //   resolve(state.accountFans)
        // } else {
        //   getMyWxAccountFansInfo().then(res => {
        //     commit('SET_ACCOUNT_FANS', res.data);
        //     resolve(state.accountFans)
        //   }).catch(res => {
        //     console.log(res);
        //     reject();
        //   });
        // }
      })
    },
    // 根据用户名登录
    getWechatLoginUrl({ dispatch, commit }) {
      return new Promise((resolve, reject) => {
        //debugger;
        //console.log('xxx', common)
        dispatch('common/lockAuth', null, { root: true }).then(()=> {
          auth.wechat.getUrl().then(res => {
            resolve(res)
          }).catch(res => {
            console.log(res);
            reject();
          });
        }).catch(()=> {
          reject('登陆出现异常，请联系客服');
        })

        // jzApi.auth.getToken({ code: params.code, state: params.state }).then((res) => {
        //   debugger;
        //   commit('SET_USER_INFO', res)
        //   commit('SET_ACCESS_TOKEN', res.access_token)
        //   console.log(user.state.userInfo)
        //   //commit('SET_ROLES', data.roles || [])
        //   //commit('SET_PERMISSIONS', data.permissions || [])
        //   resolve(res)
        // }).catch(() => {
        //   reject()
        // })
      })
    },
    /**
     * 静默登录
     */
    login({ commit, dispatch }, code) {
      uni.showLoading();
      return new Promise((resolve, reject) => {
        auth.wechat.loginByCode({code: code}).then((res) => {
          console.log(res)
          commit('SET_USER_INFO', res.user_info)
          commit('SET_ACCESS_TOKEN', res.access_token)
          commit('SET_EXPIRES_IN', res.expires_in);
          // console.log(user.state.userInfo)
          //commit('SET_ROLES', data.roles || [])
          //commit('SET_PERMISSIONS', data.permissions || [])
          uni.hideLoading();
          resolve();
        }).catch(() => {
          uni.hideLoading();
          reject();
        })
      })
    },
    /**
     * 注销
     */
    logout({ commit, dispatch }, code) {
      uni.showLoading();
      return new Promise((resolve, reject) => {
        auth.logout().then((res) => {
          console.log(res)
          dispatch('removeUserCache');
          uni.hideLoading();
          resolve();
        }).catch(() => {
          uni.hideLoading();
          reject();
        })
      })
    },
    /**
     * 超管登录
     */
    loginByOpenId({ commit, dispatch, state }, openId) {
      uni.showLoading();
      return new Promise((resolve, reject) => {
        auth.wechat.loginByOpenId({managerOpenId: state.userInfo.wxOpenid, openId: openId}).then((res) => {
          console.log(res)
          commit('SET_USER_INFO', res.user_info)
          commit('SET_ACCESS_TOKEN', res.access_token)
          commit('SET_EXPIRES_IN', res.expires_in);
          commit('SET_OTHER_CUSTOMER', true);
          // console.log(user.state.userInfo)
          //commit('SET_ROLES', data.roles || [])
          //commit('SET_PERMISSIONS', data.permissions || [])
          uni.hideLoading();
          resolve();
        }).catch(res => {
          uni.hideLoading();
          reject();
        })
      })
    },
    // 根据用户名登录
    loginByUsername({commit}, loginForm) {
      const user = encryption({
        data: loginForm,
        key: 'starstarstarstar',
        param: ['password']
      })
      const data = {
        username: user.username,
        password: user.password,
      }
      return new Promise((resolve, reject) => {
        auth.h5.loginByPassword(user.code, data, loginForm.randomStr).then(res => {
          commit('SET_USER_INFO', res.user_info)
          commit('SET_ACCESS_TOKEN', res.access_token)
          commit('SET_EXPIRES_IN', res.expires_in);
          //commit('SET_ACCESS_TOKEN', data.access_token)
          //commit('SET_REFRESH_TOKEN', data.refresh_token)
          //commit('SET_EXPIRES_IN', data.expires_in)
          //commit('SET_USER_INFO', data.user_info)
          //commit('SET_PERMISSIONS', data.user_info.authorities || [])
          //commit('CLEAR_LOCK')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    storeNextRoute({ commit, dispatch }, to) {
      return new Promise((resolve) => {
        if(to) {
          console.log('SET_NEXT_ROUTE', to)
          commit('SET_NEXT_ROUTE', to)
        } else {
          console.log('DEL_NEXT_ROUTE')
          commit('DEL_NEXT_ROUTE')
        }
        resolve();
      })
    },
    removeUserCache({ commit }) {
      commit('SET_ACCESS_TOKEN', '');
      commit('SET_USER_INFO', {});
      commit('SET_EXPIRES_IN', '');
      commit('SET_REFRESH_TOKEN', '');
      commit('SET_WALLET', {});
      commit('SET_ACCOUNT_FANS', {});
      commit('SET_OTHER_CUSTOMER', false);
    },
    setAccessToken({ commit }, accessToken) {
      commit('SET_ACCESS_TOKEN', accessToken);
    },
    setOtherCustomer({ commit }, otherCustomerFlag) {
      commit('SET_OTHER_CUSTOMER', otherCustomerFlag);
    },
    setOriginUserId({ commit}, params) {
      const originUserId = params.userId;
      const originTenantId = params.tenantId;
      commit('SET_ORIGIN_USER_ID', originUserId);
      commit('SET_ORIGIN_TENANT_ID', originTenantId);
    }
  },
  mutations: {
    SET_OTHER_CUSTOMER: (state, other_customer) => {
      state.other_customer = other_customer
      setStore({
        name: 'other_customer',
        content: state.other_customer,
        type: 'session'
      })
    },
    SET_ACCESS_TOKEN: (state, access_token) => {
      state.access_token = access_token
      setStore({
        name: 'access_token',
        content: state.access_token,
        type: 'session'
      })
    },
    SET_EXPIRES_IN: (state, expires_in) => {
      state.expires_in = new Date().getTime() + (expires_in*1000)
      setStore({
        name: 'expires_in',
        content: state.expires_in,
        type: 'session'
      })
    },
    SET_REFRESH_TOKEN: (state, rfToken) => {
      state.refresh_token = rfToken
      setStore({
        name: 'refresh_token',
        content: state.refresh_token,
        type: 'session'
      })
    },
    SET_USER_INFO: (state, userInfo) => {
      state.userInfo = userInfo
      setStore({
        name: 'userInfo',
        content: userInfo,
        type: 'session'
      })
    },
    SET_NEXT_ROUTE: (state, to) => {
      state.nextRoute = to
      setStore({
        name: 'nextRoute',
        content: to,
        type: 'session'
      })
    },
    DEL_NEXT_ROUTE: (state) => {
      state.nextRoute = undefined
      removeStore({
        name: 'nextRoute'
      })
    },
    SET_WALLET: (state, v) => {
      state.wallet = v
      setStore({
        name: 'wallet',
        content: v,
        type: 'session'
      })
    },
    SET_ACCOUNT_FANS: (state, v) => {
      state.accountFans = v
      setStore({
        name: 'accountFans',
        content: v,
        type: 'session'
      })
    },
    SET_ORIGIN_USER_ID: (state, originUserId) => {
      state.origin_user_id = originUserId
      setStore({
        name: 'origin_user_id',
        content: state.origin_user_id,
        type: 'session'
      })
    },
    SET_ORIGIN_TENANT_ID: (state, origin_tenant_id) => {
      state.origin_tenant_id = origin_tenant_id
      setStore({
        name: 'origin_tenant_id',
        content: state.origin_tenant_id,
        type: 'session'
      })
    },
    // SET_MENU: (state, params = {}) => {
    //   let {menu, type} = params;
    //   if (type !== false) state.menu = menu
    //   setStore({
    //     name: 'menu',
    //     content: menu,
    //     type: 'session'
    //   })
    // },
    // SET_MENU_ALL: (state, menuAll) => {
    //   state.menuAll = menuAll
    // },
    // SET_ROLES: (state, roles) => {
    //   state.roles = roles
    // },
    // SET_PERMISSIONS: (state, permissions) => {
    //   const list = {}
    //   for (let i = 0; i < permissions.length; i++) {
    //     list[permissions[i].authority] = true
    //   }
    //
    //   state.permissions = list
    //   setStore({
    //     name: 'permissions',
    //     content: list,
    //     type: 'session'
    //   })
    // }
  }

}
export default {
  namespaced: true,
  state: user.state,
  mutations: user.mutations,
  actions: user.actions
}
