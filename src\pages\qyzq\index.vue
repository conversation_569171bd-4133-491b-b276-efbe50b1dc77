<template>
  <view class="content">
    <uni-search-bar placeholder="搜索推介" clearButton="auto" @confirm="search" @clear="searchClear"></uni-search-bar>
    <scroll-view class="list" scroll-y @scrolltolower="getData(true)">
      <swiper class="swiper-top" indicator-dots="true" autoplay="true" interval="3000">
        <swiper-item v-for="(item, index) in items" :key="index">
          <img :src="$website.ossDomain + item.mediaPath" mode="aspectFill" :lazy-load="true"></img>
        </swiper-item>
      </swiper>
      <view class="notice-box">
        <text class="ylh-icon icon-voice"></text>
        <swiper class="swiper-notice" vertical="true" autoplay="false" duration="500" interval="4000">
          <swiper-item v-for="(item, index) in notices" :key="index">
            <text class="notice-text">{{ item.textContent }}</text>
          </swiper-item>
        </swiper>
      </view>
      <!--推介轮播-->
      <view class="promo-search">
        <com-promo-search @search="orderQuery"></com-promo-search>
      </view>
      <!--<uni-section title="今日推介" type="line"></uni-section>-->
      <view class="promo-list">
        <view v-for="(item, index) in promoList" :key="index" v-if="item.lastGamePromo">
          <com-promo-series :index.sync="index" :item.sync="item"
                            :week-rate.sync="queryParams.orderColumn!=='monthRate'"
                            @buyGame="buy(item.lastGamePromo)"></com-promo-series>
        </view>
      </view>
      <uni-load-more v-if="queryParams.size > 0 && promoList.length > 0" :status="loadStatus"></uni-load-more>
      <charge ref="popup" title="余额不足，请及时充值"></charge>
    </scroll-view>
  </view>
</template>

<script>
import uniLoadMore from "@/components/uni-load-more";
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromoSeries from './components/com-promo-series'
import comPromoSearch from './components/com-promo-search'
import {getSimpleAllList, getSeriesGame} from '@/common/request/promo-series'
import {getAdItemsByType} from '@/common/request/ad'
import promoBuy from '../home/<USER>/promo-buy'
import charge from "../customer/components/charge";
import uniSearchBar from '@/components/uni-search-bar'
import fuiLoading from '@/components/fui-loading'
import {getDictListByType} from "@/common/request/admin";
import {isEmpty} from "@/util/validate";

export default {
  components: {uniSection, uniCard, comPromoSeries, charge, uniSearchBar, comPromoSearch, fuiLoading, uniLoadMore},
  mixins: [promoBuy],
  data() {
    return {
      promoList: [],
      items: [],
      notices: [],
      groups: [],
      seriesHasGameMap: {},
      loadStatus: 'more',
      loadFlag: false,
      queryParams: {
        current: 0,
        size: 8,
        keywords: "",
        //type: "足球",
        orderColumn: undefined,
        orderAsc: false
      }
    }
  },
  onLoad() {
    this.getSimpleAllList();
    this.getData();
  },
  onShow() {
    this.getTitle();
  },
  methods: {
    getTitle() {
      getDictListByType('wechat_index_title').then((res) => {
        if (res.data && res.data.length > 0) {
          const title = res.data[0].value;
          if (title) {
            uni.setNavigationBarTitle({
              title: title
            })
          }

        }

      }).catch(res => {
      });

    },
    getData(nextPage) {
      if (this.loadFlag) {
        return;
      }
      this.loadFlag = true;
      if (nextPage) {
        this.queryParams.current += 1;
      } else {
        this.loadStatus = 'more';
        this.promoList = [];
        this.queryParams.current = 1;
      }
      if (this.loadStatus !== 'more') {
        this.loadFlag = false;
        return;
      }
      uni.showLoading();
      getSeriesGame(this.queryParams).then(res => {
        this.loadFlag = false;
        uni.hideLoading();
        const data = res.data;
        if (!isEmpty(data)) {
          this.promoList = this.promoList.concat(data);
        }
        if (!data || data.length < this.queryParams.size) {
          this.loadStatus = 'noMore'
        }
      }).catch(res => {
        this.loadFlag = false;
        uni.hideLoading();
      });
    },
    getSimpleAllList() {
      this.groups = [];
      getAdItemsByType(0).then(res => {
        this.items = res.data;
      }).catch(res => {
      });
      getAdItemsByType(1).then(res => {
        this.notices = res.data;
      }).catch(res => {
      });
    },
    search(res) {
      // uni.showToast({
      //     title: '搜索：' + res.value,
      //     icon: 'none'
      // });
      this.queryParams.keywords = res.value;
      this.getData();
    },
    searchClear() {
      this.queryParams.keywords = null;
      this.getData();
    },
    getSearchParams() {
      let params = {orderColumn: 'hasPromotion'}
    },
    orderQuery(orderParam) {
      this.queryParams.orderColumn = orderParam.orderColumn;
      this.queryParams.orderAsc = orderParam.orderAsc;
      this.getData();
    }
  }
}
</script>

<style lang="scss">
.list {
  height: 100%;
  padding-bottom: 96rpx;

  .promo-search {
    //margin: 0 30rpx;
    margin-top: 4rpx;
    box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e5e5;
  }
}

.com-title {
  width: 100%;

  .ylh-icon {
    color: $primary-color-dark;
    font-size: $font-base -2rpx;
    margin-right: $page-row-spacing - 4rpx;
  }
}

.swiper-top {
  width: 100%;
  height: 358rpx !important;;

  img {
    width: 100%;
    height: 100%;
  }
}

.notice-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 18rpx;

  .icon-voice {
    width: 44rpx;
  }

  .swiper-notice {
    flex: auto;
    height: 62rpx;
    line-height: 62rpx;
  }

  .notice-text {
    color: #dd524d;
  }
}
</style>
