<template>
    <view class="content">
        <scroll-view v-if="qrCode" class="scroll-box" scroll-y>
            <view class="jz-row qrcode">
                <image :src="$website.ossDomain + qrCode.mediaPath" mode="widthFix" @click=""></image>
            </view>
            <view class="jz-row m-t">
                <view class="text1">客服二维码<text class="color-red">（长按识别）</text></view>
            </view>
            <view class="jz-row m-t">
<!--                <view class="text2" v-html="qrCode.textContent"></view>-->
                <rich-text class="text2" :nodes="qrCode.textContent"></rich-text>
            </view>
        </scroll-view>
        <uni-fab v-if="!$Route.isTab" :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniFab from '@/components/uni-fab'
import {getAdItemsByType} from "@/common/request/ad";

export default {
    components: {uniFab},
    data() {
        return {
            qrCode: {},
        }
    },
    computed: {
        content() {
            return {
                iconPath: this.$website.ossDomain + '/admin/sys-file/fb-mp/backward.png'
            }
        }
    },
    onLoad() {
        this.getData();
    },
    methods: {
        getData() {
            getAdItemsByType(2).then(res=> {
                this.qrCode = res.data[0];
            }).catch(res=>{});
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    padding-top: 60rpx;
}
.qrcode {
    padding: 40rpx 50rpx;
    image {
        width: 400rpx;
    }
}
.jz-row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.text1 {
    font-size: $font-lg;
    .color-red {
        color: #dd524d;
    }
}
.text2 {
    padding: 40rpx 20rpx;
    color: $font-color-light;
}
</style>