import Vue from 'vue'
import App from './App'

import {router,RouterMount} from './router.js'
Vue.use(router)

import ylhfont from '@/common/css/iconfont/iconfont.css'

/* 公共Mixin */
import Mixin from './mixins/public';
Vue.use(Mixin);

import store from '@/store'
Vue.prototype.$store = store;

/* 过滤器 */
import * as jzFilter from '@/common/jz-filter'
Object.keys(jzFilter).forEach(key => {
  Vue.filter(key, jzFilter[key])
})

import website from '@/common/website'
Vue.prototype.$website = website;

import jzCommonCss from '@/common/css/jz-common.scss'
//注册全局组件
import jzMask from './components/jz-mask'
import pageHead from './components/page-head'
Vue.component('jzMask', jzMask)
Vue.component('pageHead', pageHead)

import wechatMp from './components/container/wechatMp'
Vue.component('wechatMp', wechatMp)

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
  ...App
})
//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app,router,'#app')
// #endif

// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif
