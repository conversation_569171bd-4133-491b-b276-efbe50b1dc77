<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y  @scrolltolower="getHistoryGameList(true)">
            <com-promo-series-head :promo-series-id="promoSeriesId" :game="game"></com-promo-series-head>

            <view class="jz-row bg-white m-t" v-if="(game.priceWeek || game.priceMonth)">
                <uni-section title="优惠订阅" type="line"></uni-section>
                <view class="discount-buy-box">
                    <view class="jz-button jz-button-week" v-if="game.priceWeek" @click.stop="buy(game, '包周')">
                        <text class="ylh-icon icon-coin"></text> {{game.priceWeek}}包周
                    </view>
                    <view class="jz-button jz-button-month" v-if="game.priceMonth" @click.stop="buy(game, '包月')">
                        <text class="ylh-icon icon-coin"></text> {{game.priceMonth}}包月
                    </view>
                </view>
            </view>

            <view class="jz-row bg-white m-t" v-if="game">
                <uni-section title="最新赛事" type="line">
                    <view class="">
                        <text style="color:orangered;font-weight:bold;padding-right:4px">{{game.playTime | date('yyyy-MM-dd hh:mm')}}</text>
                    </view>
                </uni-section>
                <view class="game-box">
                    <view class="game-box-content">
                        <view class="game-play-time">
                            {{game.gameName}}
                        </view>
                        <view class="game-home-away">
                            <text class="game-home deg-box">{{game.home}}</text>
                            <text class="game-vs">VS</text>
                            <text class="game-away deg-box">{{game.away}}</text>
                        </view>
                        <view class="game-score m-t" v-if="game.score">
                            比分：{{game.score}}
                        </view>
                    </view>
                </view>
            </view>

            <view class="promo-reason m-t" v-if="game.promotion">
                <uni-section title="推介理由" type="line"></uni-section>
                <view class="promo-content-combine">
                    <view>
                        <rich-text :nodes="game.promotion"></rich-text>
                    </view>
                </view>
            </view>
            <view class="promo-reason m-t">
                <uni-section title="方案" type="line">
                    <view class="" v-if="expired.h">
                        <text style="color:#888888;padding-right:4px">距离开赛</text>
                        <uni-countdown :font-size="12" :show-day="expired.d>0" :day="expired.d" :hour="expired.h" :minute="expired.m" :second="expired.s" color="#FFFFFF" background-color="#007AFF" @timeup="timeUp" />
                    </view>
                </uni-section>
                <view class="promo-content-combine">
                    <view class="promo-remark" v-if="game.gameRemark">
                        <rich-text :nodes="game.gameRemark"></rich-text>
                    </view>
                    <view v-else class="promo-sec-bg">
                        <view class="promo-sec">
                            <view v-if="!canBuy(game)"></view>
                            <view v-else class="jz-button full-border" @click.stop="buy(game)">
                                <text class="ylh-icon icon-lock"></text>
                                <view class="jz-button-text">支付 {{game.priceDay}}金币 查看方案</view>
                            </view>
                        </view>
                    </view>
                    <view class="game-right" v-if="!isEmpty(game.result)">
                        <view class="game-result-icon" :class="resultClass(game.result)">{{game.result|gameResult}}</view>
                    </view>
                </view>
            </view>

            <view class="jz-row mention-box m-t">
                <uni-section title="购买须知" type="line"></uni-section>
                <view class="mention">
                    观点仅供参考，投注需谨慎！本观点仅服务于购买中国竞彩以及北京单场的用户，购彩请前往彩票店。
                </view>
            </view>

            <view class="m-t" v-if="historyPromoList && historyPromoList.length > 0">
                <uni-section title="历史赛事" type="line"></uni-section>
                <view class="promo-list">
                    <com-promo :dateFlag="true" :item="itemK" v-for="(itemK, index) in historyPromoList" :key="index"></com-promo>
                </view>
                <uni-load-more v-if="pageInfo.size > 0 && historyPromoList.length > 0" :status="loadStatus"></uni-load-more>
            </view>
            <charge ref="popup" title="余额不足，请及时充值"></charge>
            <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
        </scroll-view>
<!--        <uni-fab v-if="game.priceDay" :pattern="{}" horizontal="right" vertical="bottom" icon="">-->
<!--            <view class="buy-box" @click.stop="buy(game)">-->
<!--                <view class="ylh-icon icon-gouwuchecar"></view>-->
<!--                <view class="buy-text">支付 {{game.priceDay}}金币 查看方案</view>-->
<!--            </view>-->
<!--        </uni-fab>-->
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from './components/com-promo'
import uniFab from '@/components/uni-fab'
import uniLoadMore from '@/components/uni-load-more'
import uniCountdown from '@/components/uni-countdown'
import {
    getGameItemBeforePlayTime,
    getGameResultText, getHistoryGameList
} from "@/common/request/promo-series";
import {isEmpty} from "@/util/validate";
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";
import promoBuy from "@/pages/home/<USER>/promo-buy";
import charge from "@/pages/customer/components/charge";
import ComPromoWinTimes from "@/pages/qyzq/components/com-promo-win-times";
import ComPromoSeriesHead from './components/com-promo-series-head';

export default {
    components: {ComPromoWinTimes, uniSection, uniCard, comPromo, uniFab, uniLoadMore,charge,uniCountdown,ComPromoSeriesHead},
    mixins: [promoBuy],
    data() {
        return {
            rawPay: true,
            results: [],
            gameId: '',
            promoSeriesId: '',
            game: {
                gameRemark: 'x'
            },
            expired: {
                d: undefined,
                h: undefined,
                m: undefined,
                s: undefined
            },
            winHistory: {
                times: 10,
                winTimes: 0,
            },
            historyPromoList: [],
            loadStatus: 'more',
            loadFlag: false,
            pageInfo: {
                current: 0,
                size: 8,
            },

        }
    },
    onLoad() {
        let options = this.getParams();
        this.gameId = options.id;
        this.promoSeriesId = options.promoSeriesId;
    },
    onShow() {
        if(this.gameId) {
            this.getGameItem();
        }
        if(this.promoSeriesId) {
            this.getResults();
            this.getHistoryGameList();
        }
    },
    methods: {
        canBuy(item) {
            const gamePromo = item;

            //没有赛事不能买
            if(gamePromo == null) {
                return false;
            }
            //未购买过
            const isNotBuy = isEmpty(item.buyTime);

            // if(item.type === '篮球' && isNotBuy) {
            //     return true;
            // }

            //未到开始时间
            let gameNotStart = false;
            if(gamePromo.playTime) {
                let now = new Date();
                //console.log('canBuy', gamePromo.playTime)
                let playTime = new Date(gamePromo.playTime.replace(/-/g, "/"));
                playTime = new Date(playTime.setHours(playTime.getHours()+2))
                gameNotStart = playTime > now;
            }

            //已出料 && 未到开始时间 && 未发布结果 && 未购买过
            console.log(item.hasPromotion,gameNotStart,isEmpty(gamePromo.result),isNotBuy)
            return item.hasPromotion && gameNotStart && isEmpty(gamePromo.result) && isNotBuy
        },
        getGameItem() {
            const me = this;
            getGameItemBeforePlayTime({id: this.gameId}).then(res=> {
                me.game = res.data;
                me.calcTime(me.game.playTime);
            }).catch(res=>{});
        },
        getResults() {
            const me = this;
            getGameResultText(me.promoSeriesId).then(res=> {
                if(res.data) {
                    me.results = res.data.split('');
                    let times = 0;
                    me.winHistory.times = me.results.length;
                    for(let i = 0; i < me.results.length; i++) {
                        if(this.results[i]==='W') {
                            times++;
                            me.winHistory.winTimes++;
                        }
                    }
                    if(times > 0) {
                        me.winTimes = times+'连胜';
                    }
                }
            }).catch(res=>{});
        },
        getHistoryGameList(nextPage) {
            if(this.loadFlag){
                return;
            }
            this.loadFlag = true;
            if(nextPage) {
                this.pageInfo.current += 1;
            } else {
                this.loadStatus = 'more';
                this.historyPromoList = [];
                this.pageInfo.current = 1;
            }
            if(this.loadStatus !== 'more') {
                this.loadFlag = false;
                return;
            }
            this.pageInfo.promoSeriesId = this.promoSeriesId;
            uni.showLoading();
            getHistoryGameList(this.pageInfo).then(res=> {
                this.loadFlag = false;
                const data = res.data.records;
                if(!isEmpty(data)) {
                    this.historyPromoList = this.historyPromoList.concat(data);
                }
                if(!data || data.length < this.pageInfo.size) {
                    this.loadStatus = 'noMore'
                }
                uni.hideLoading();
            }).catch(res=>{
                this.loadFlag = false;
                uni.hideLoading()
            });
        },
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
        calcTime(d) {
            const now = new Date().getTime();
            const end = new Date(d).getTime();
            const leftTime = end - now;
            if (leftTime >= 0) {
                this.expired.d = Math.floor(leftTime / 1000 / 60 / 60 / 24);
                this.expired.h = Math.floor(leftTime / 1000 / 60 / 60 % 24);
                this.expired.m = Math.floor(leftTime / 1000 / 60 % 60);
                this.expired.s = Math.floor(leftTime / 1000 % 60);
            }
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
}
.bg-white {
    background-color: #FFFFFF;
}
.jz-row {
    width: 100%;
}
.game-box {
    padding-top: 16rpx;
    .game-box-content {
        //border-radius: 12rpx;
        //background-color: #cccccc;
        padding: 8rpx 48rpx 16rpx 36rpx;
        //box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
    }
    .game-play-time {
        font-size: $font-base;
        color: #888888;
        padding: 8rpx 4rpx 16rpx 4rpx;
    }
    .game-home-away {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        .deg-box {
            position: relative;
            padding: 4rpx 0;
            font-size: $font-base + 2rpx;
            z-index: 1;
        }
        .deg-box::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: -1;
            transform: skew(-12deg);
        }
        .game-home {
            flex: 1;
            color: #fff;
        }
        .game-home::before {
            background: #0542b1;
        }
        .game-vs {
            width: 100rpx;
        }
        .game-away {
            flex: 1;
            color: #fff;
        }
        .game-away::before {
            background: #ff0000;
        }
    }
    .game-score {
        font-size: $font-sm;
        color: #4a4c55;
        border-radius: 12rpx;
        //background-color: #cccccc;
        padding: 8rpx 48rpx 8rpx 36rpx;
        box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
    }
}
.series {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 10rpx 8rpx 12rpx 14rpx;
    .series-top {
        display: flex;
        flex-direction: row;
        .series-img {
            width: 120rpx;
            image {
                width: 120rpx;
                height: 120rpx;
            }
        }
        .series-content-box {
            display: flex;
            flex-direction: row;
            flex: 1;
            .series-memo-box {
                flex: 1;
                display: flex;
                flex-direction: column;
                .top-title {
                    flex: 1;
                    font-size: $font-lg + 8rpx;
                    font-weight: 700;
                    padding: 0 8rpx;
                }
                .bottom-tag {
                    flex: 1;
                    padding: 0 8rpx;
                    font-size: $font-base;
                    display: flex;
                    align-items: center;
                    flex-direction: row;
                    .game-tag {
                        display: inline-block;
                        align-items: center;
                        justify-content: center;
                        background: #ff0000;
                        color: #ffffff;
                        padding: 4rpx 22rpx;
                        border-radius: 8rpx;
                    }
                }
            }
            .series-add {
                width: 148rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-right: 8rpx;
                .jz-button {
                    font-size: $font-base;
                    padding: 4rpx;
                    background-color: $primary-color-base;
                    color: $primary-color-white;
                    .ylh-icon {
                        font-size: $font-base;
                    }
                    .jz-button-text {
                        padding-left: 4rpx;
                    }
                }
                .full-border {
                    border: 4rpx solid $primary-color-base;
                }
                .followed {
                    border: 2rpx solid $primary-color-base;
                    background: transparent;
                    color: $primary-color-base;
                }
            }
        }
    }
    .series-bottom {
        padding: 24rpx 10rpx 12rpx 10rpx;

        .series-memo {
            border-radius: 12rpx;
            background-color: #f7f7f7;
            padding: 8rpx 48rpx 16rpx 36rpx;
            //box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
            display: flex;
            flex-direction: row;
            .text-tag {
                width: 80rpx;
            }
            .series-memo-content {
                padding-left: 8rpx;
            }
        }
    }


}
.buy-box {
    display: flex;
    flex-direction: row;
    color: $primary-color-white;
    padding: 12rpx 16rpx;
    .ylh-icon {
        font-size: 36rpx;
    }
    .buy-text {
        padding-left: 4rpx;
    }
}



.text-tag {
    display: inline-block;
    align-items: center;
    justify-content: center;
    background: $primary-color-base;
    color: #ffffff;
    padding: 0 8rpx;
    font-size: $font-sm;
}
.discount-buy-box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20rpx 60rpx;
    align-items: center;
    .jz-button {
        width: 40%;
    }
    .jz-button-week {
        background-color: #e7234e;
        padding: 16rpx 8rpx;
    }
    .jz-button-month {
        background-color: #F79A07;
        padding: 16rpx 8rpx;
    }
}
.promo-reason {
    background-color: #fff;
    .promo-content {
        padding: 10rpx 24rpx;
        font-size: $font-lg;
        background-color: #fff;
        text-indent: 2em;
        color: $font-color-light;
    }
    .promo-content-combine {
        padding: 24rpx;
        line-height: 48rpx;
        background: transparent;
        display: flex;
        .promo-remark {
            flex: 1;
        }
        .game-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 120rpx;
            padding-left: 20rpx;
            padding-right: 10rpx;
            .game-result-icon {
                display:flex;
                justify-content:center;
                border-radius:50%;
                width:60rpx;
                height:60rpx;
                line-height:54rpx;
                font-size: 32rpx;
                font-weight: 600;
                margin-right: 20rpx;
            }
            .color-red {
                color:red;
                border:2rpx solid red;
            }
            .color-black {
                color:#000;
                border:2rpx solid #000;
            }
            .color-green {
                color: #4cb84b;
                border:2rpx solid #4cb84b;
            }
        }
    }
}
.promo-sec-bg {
    width: 100%;
    display: flex;
    //background-position: center bottom;
    //background-size: cover;
    background-repeat: no-repeat;
    background-image: url($media-url + '/admin/sys-file/fb-mp/promo_sec_bg.png');
    background-size:100% 100%;
    .promo-sec {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 120rpx 0;
        .jz-button {
            background: transparent;
            border: none;
            color: $primary-color-base;
            .ylh-icon {
                font-size: 62rpx;
            }
        }
        .jz-button-text {
            font-weight: 700;
            font-size: $font-lg + 6rpx;
            padding-left: 4rpx;
        }
    }
}
.mention-box {
    background-color: #fff;
    padding-bottom: 40rpx;
    .mention {
        padding: 24rpx;
        color: $font-color-disabled;
        font-size: $font-sm;
    }
}
</style>
