    upstream cloud-gateway{
		#server *********:9999;
        server *********:9999;
        server *********:9999;
        #server localhost:2180 max_fails=10 fail_timeout=1h;
        #server localhost:2080 max_fails=10 fail_timeout=1h backup;
        #server *********:9999 max_fails=10 fail_timeout=1h backup;
    }
	#2080 jm
	#2180 yj
    server {
        listen       443 ssl;
        server_name  xg.365tt365tt.com;
        ssl_certificate      xg.365tt365tt.com.pem;
        ssl_certificate_key  xg.365tt365tt.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";

		root /home/<USER>/cloud-ui-xg/;

		
		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}
		location ~* ^/(theater|theater1|theater-test|code|auth|admin|gen|daemon|tx|act|monitor|mp|job|pay) {
			proxy_pass http://cloud-gateway;
			proxy_set_header Host $http_host;
				proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_connect_timeout 15s;
			proxy_send_timeout 15s;
			proxy_read_timeout 15s;
    	}
    }
    server {
        listen       443 ssl;
        server_name  xg.yj-star.com;
        ssl_certificate      xg.yj-star.com.pem;
        ssl_certificate_key  xg.yj-star.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";

		root /home/<USER>/cloud-ui-xg/;

		
		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}
		location ~* ^/(theater|theater1|theater-test|code|auth|admin|gen|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
			proxy_pass http://*********:9999;
			proxy_set_header Host $http_host;
				proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_connect_timeout 15s;
			proxy_send_timeout 15s;
			proxy_read_timeout 15s;
    	}
    }