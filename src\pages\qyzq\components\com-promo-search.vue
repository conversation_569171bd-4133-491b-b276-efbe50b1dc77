<template>
    <view class="promo-search-box">
        <uni-grid :column="3" :highlight="false" :show-border="false" @change="change">
            <uni-grid-item v-for="(item, index) in groups" :index="index" :key="index">
                <view class="series-item empty-border" :class="{'full-border': index === selectedIndex}">
                    <view class="ylh-icon" :class="item.icon"></view>
                    <view class="text">{{ item.text }}</view>
                </view>
            </uni-grid-item>
        </uni-grid>
        <!--<jz-confirm-modal v-if="confirmObj.config.display" :config="confirmObj.config" :inputs="confirmObj.inputs"-->
        <!--                  :params="confirmObj.params" @submit="confirmChange"-->
        <!--                  @close="confirmObj.config.display=false"></jz-confirm-modal>-->
    </view>

</template>

<script>
import UniGrid from '@/components/uni-grid/uni-grid'
import UniGridItem from '@/components/uni-grid/uni-grid-item'
import website from "@/common/website";
// import JzConfirmModal from "@/components/jz-confirm-modal";

export default {
    name: "comPromoSearch",
    components: {UniGrid,UniGridItem},
    props: {
    },
    data() {
        return {
            groups: website.comPromoSearch.group,
            selectedIndex: -1,
            // confirmObj: {
            //     config: {
            //         display: false,
            //         title: "修改门店",
            //         buttonText: '提交'
            //     },
            //     inputs: [{
            //         vModel: 'shopNumber',
            //         icon: 'icon-mendian',
            //         maxlength: 10,
            //         placeholder: '请输入门店编号'
            //     }],
            //     params: {
            //         shopNumber: ''
            //     }
            // },
        }
    },
    methods: {
        // getCount(item) {
        //     return this.seriesHasGameMap[item.id+''];
        // },
        change(e) {
            let { index } = e.detail
            this.selectedIndex = index;
            let item = this.groups[this.selectedIndex];
            if(this.selectedIndex === 4) {
                let text = item.text;
                switch (text) {
                    default:
                    case "金币排序": item.text = "金币降序"; item.orderAsc = false; break;
                    case "金币升序": item.text = "金币降序"; item.orderAsc = false; break;
                    case "金币降序": item.text = "金币升序"; item.orderAsc = true; break;
                }
            }
            else {
                this.groups[4].text = '金币排序';
            }
            const { orderColumn, orderAsc} = item;
            this.$emit('search', { orderColumn, orderAsc });
        },
        // confirmChange(data) {
        //     this.confirmObj.config.display = false;
        //     if (data && data.shopNumber) {
        //
        //     }
        // },
    }
}
</script>

<style lang="scss">
.promo-search-box {
    padding: 10rpx;
    background-color: #fff;
    border-radius: 2px;
}
.empty-border {
  border: 1px solid #fff;
}
.series-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    .ylh-icon {
        font-size: 54rpx;
        color: #e7234e;
    }
    .text {
        padding-top: 20rpx;
        font-size: $font-base;
        font-weight: 500;
        color: #4a4c55;
    }
}
.full-border {
  border-color: #d8d7d7;
  border-radius: 8rpx;
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.1);
}
.order-cnt {
    position: absolute;
    top: 10upx;
    right: 30upx;
}
</style>