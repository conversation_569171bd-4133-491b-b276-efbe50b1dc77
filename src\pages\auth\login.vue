<template>
  <view v-if="config.isH5" class="content">
    <view class="form-container">
      <view class="input-row form-title">
        <text>用户登录</text>
      </view>
      <uni-forms :modelValue="loginForm" :labelWidth="90">
        <uni-forms-item required label="手机号：" name="gameName" labelAlign="right">
          <uni-easyinput type="text" v-model="loginForm.username" placeholder="请输入您的手机号" />
        </uni-forms-item>
        <uni-forms-item required name="promoSeriesId" label="密码：" labelAlign="right">
          <uni-easyinput type="password" v-model="loginForm.password" placeholder="请输入密码" />
        </uni-forms-item>
      </uni-forms>
      <view class="input-row border">
        <Verify
            @success="verifySuccess"
            :mode="'pop'"
            :captchaType="loginForm.randomStr"
            :imgSize="{ width: '330px', height: '155px' }"
            ref="verify"
        />
      </view>
    </view>
    <view class="btn-row">
      <button type="primary" class="primary" :loading="loginBtnLoading" @tap="handleLogin">登录</button>
    </view>
  </view>
</template>

<script>
import {mapState, mapMutations} from 'vuex';
import uniForms from '@/components/uni-forms/uni-forms.vue'
import uniFormsItem from '@/components/uni-forms-item/uni-forms-item.vue'
import uniEasyinput from '@/components/uni-easyinput/uni-easyinput.vue'
import Verify from "@/components/verifition/Verify.vue";
import {isEmpty} from "@/util/validate";

export default {
  components: {uniForms,uniFormsItem, Verify, uniEasyinput},
  data() {
    return {
      loginType: 0,
      loginTypeList: ['免密登录', '密码登录'],
      loginForm: {
        username: "",
        password: "",
        code: "",
        randomStr: "clickWord", //blockPuzzle
      },
      mobile: '',
      code: '',
      providerList: [],
      hasProvider: false,
      username: '',
      password: '',
      positionTop: 0,
      isDevtools: false,
      codeDuration: 0,
      loginBtnLoading: false,
      hasAppleLogin: false,
      needCaptcha: uni.getStorageSync('uni-needCaptcha'),
      captchaing: false,
      captchaBase64: '',
      captchaText: ''
    }
  },
  onShow() {
    //this.getData();
  },
  methods: {
    getData() {
      this.$store.dispatch('user/getWallet').then((wallet) => {
        this.wallet = wallet;
      }).catch(res => {
      });
      this.$store.dispatch('user/getAccountFans').then((data) => {
        this.accountFans = data;
      }).catch(res => {
      });

    },
    openPopup() {
      this.$refs.popup.openPopup()
    },
    handleLogin() {
      this.$refs.verify.show();
    },
    verifySuccess(params) {
      this.loginForm.code = params.captchaVerification;

      //this.$refs.form.validate().then(res=>{
      //  console.log('表单数据信息：', res);
      //}).catch(err =>{
      //  console.log('表单错误信息：', err);
      //})
      this.$store.dispatch("user/loginByUsername", this.loginForm).then(() => {
        const nextRoute = this.$store.getters.nextRoute;
        if (!isEmpty(nextRoute)) {
          this.gotoRoute(nextRoute, true)
        } else {
          this.gotoName('index')
        }
      });
    },
  }
}
</script>

<style lang="scss">
.content {
  width: 100%;
  .form-container {
    background-color: #FFFFFF;
    padding: 48rpx 38rpx 0 24rpx;

    .form-title {
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: $font-lg;
      font-weight: 700;
    }
    .uni-forms {
      padding-top: 24rpx;
      .uni-forms-item .uni-forms-item__label .label-text {
        font-size: $font-lg;
      }
    }

  }
  .btn-row {
    margin-top: 25px;
    padding: 10px;
  }
  .input-group {
    background-color: #ffffff;
    margin-top: 20px;
    position: relative;
  }
  .input-group::before {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #c8c7cc;
  }
  .input-group::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #c8c7cc;
  }
  .input-row {
    display: flex;
    flex-direction: row;
    position: relative;
    /* font-size: 18px; */
    height: 40px;
    line-height: 40px;
  }
  .input-row .title {
    width: 70px;
    padding-left: 15px;
  }
  .input-row.border::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 8px;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #c8c7cc;
  }
}
.login-type {
  display: flex;
  justify-content: center;
}

.login-type-btn {
  line-height: 30px;
  margin: 0px 15px;
}

.login-type-btn.act {
  color: #0FAEFF;
  border-bottom: solid 1px #0FAEFF;
}

.send-code-btn {
  width: 120px;
  text-align: center;
  background-color: #0FAEFF;
  color: #FFFFFF;
}

.action-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.action-row navigator {
  color: #007aff;
  padding: 0 10px;
}

.oauth-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.oauth-image {
  position: relative;
  width: 50px;
  height: 50px;
  border: 1px solid #dddddd;
  border-radius: 50px;
  background-color: #ffffff;
}

.oauth-image image {
  width: 30px;
  height: 30px;
  margin: 10px;
}

.oauth-image button {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.captcha-view {
  line-height: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
  background-color: #f3f3f3;
}
</style>
