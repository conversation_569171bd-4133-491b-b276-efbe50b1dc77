<template>
  <uni-card isShadow>
    <template v-slot:header>
      <view class="com-card-title">
        <view class="title-row">
          <view class="title-left">
            <view class="header-name">
              <text class="header-name-text">{{item.nickName}} - {{item.tenantName}}({{item.tenantId}})</text>
            </view>
          </view>
          <view class="title-right">
            <view class="jz-button jz-button-remove" v-if="item.backup" @click="removeBackup(item.userId, item.tenantId)">
              <text class="ylh-icon icon-remove"></text>
              <text class="jz-button-text">移除</text>
            </view>
            <view class="jz-button jz-button-add" v-else @click="addBackup(item.userId, item.tenantId)">
              <text class="ylh-icon icon-add"></text>
              <text class="jz-button-text">加入</text>
            </view>
          </view>
        </view>
        <view class="title-row">
          <text class="created-time">{{ item.createTime }}</text>
        </view>
      </view>
    </template>
    <view class="com-card-content">
      <view class="card-content-box">
        <view class="jz-row">
          <view class="field-box">
            <text class="field-label">id：</text>
            <text class="field-value">{{item.userId}}</text>
          </view>
        </view>
        <view class="jz-row">
          <view class="field-box">
            <text class="field-label">warningTime：</text>
            <text class="field-value">{{item.lastWarningTime}}</text>
          </view>
        </view>
        <view class="jz-row">
          <view class="field-box">
            <text class="field-label">总额：</text>
            <text class="field-value">{{item.amount}}</text>
          </view>
          <view class="field-box">
            <text class="field-label">单笔：</text>
            <text class="field-value">{{item.maxAmount}}</text>
          </view>
        </view>
        <view class="jz-row">
          <view class="field-box">
            <text class="field-label">次数：</text>
            <text class="field-value">{{item.payCount}}</text>
          </view>
          <view class="field-box">
            <text class="field-label">backupAmt：</text>
            <text class="field-value">{{item.backupAmount}}</text>
          </view>
        </view>
        <view class="jz-row">
          <view class="field-box">
            <text class="field-label">coin：</text>
            <text class="field-value">{{item.coin}}</text>
          </view>
          <view class="field-box">
            <text class="field-label">b-coin：</text>
            <text class="field-value">{{item.backupCoin}}</text>
          </view>
        </view>

        <view class="bh-list">
          <view class="list-item" v-for="(bhItem, index) in item.balanceHistoryList" :key="index">
            <view class="list-item-view">
              <view class="cell-time">{{bhItem.createdTime}}</view>
              <view class="list-cell cell-inline">{{bhItem.remark}}</view>
            </view>
            <view class="list-item-view">
              <view class="list-cell">changeCoin：{{bhItem.changeCoin}}</view>
              <view class="list-cell">remainCoin：{{bhItem.remainCoin}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <template v-slot:footer>

    </template>
  </uni-card>
</template>

<script>
import UniCard from '@/components/uni-card'
import uniLoadMore from '@/components/uni-load-more'
import jzEmpty from '@/components/jz-empty';
import {isEmpty} from "@/util/validate";
import {showMsgConfirm} from "@/common/jz-util"
import {addBackupUser, removeBackupUser} from "@/common/request/admin";

export default {
  name: "comBackupUser",
  components: {UniCard, uniLoadMore, jzEmpty},
  props: {
    item: {
      type: Object,
      default: function () {
        return {};
      }
    },
  },
  methods: {
    addBackup(userId, tenantId) {
      const me = this;
      showMsgConfirm(
          '操作提示',
          '是否要加入',
          '再想想',
          '确认',
          function() {
          },
          function() {
            uni.showLoading();
            addBackupUser({userId, tenantId}).then(res=> {
              me.afterChange(userId, tenantId);
            }).finally(()=> {uni.hideLoading();});
          }
      );

    },
    removeBackup(userId, tenantId) {
      const me = this;
      showMsgConfirm(
          '操作提示',
          '是否要移除',
          '再想想',
          '确认',
          function() {
          },
          function() {
            uni.showLoading();
            removeBackupUser({userId, tenantId}).then(res=> {
              me.afterChange(userId, tenantId);
            }).finally(()=> {uni.hideLoading();});
          }
      );

    },
    afterChange(userId, tenantId){
      this.$emit("afterChange", userId, tenantId);
    },
  }
}
</script>

<style lang="scss">
.com-card-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  .title-row {
    display: flex;
    justify-content: space-between;
    .title-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .title-right {
      align-items: flex-end;
    }
  }


  .header-name {
    display: flex;
    flex: auto;
    align-items: center;
    padding-left: 12rpx;

    .header-name-text {
      flex: 1;
      font-size: $uni-font-size-lg;
      color: #000;
      font-weight: 500;
    }
  }

  .created-time {
    width: 240rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: right;
    color: $font-color-light;
    font-size: $font-sm;
  }
}

.com-card-content {
  padding: 12rpx 12rpx 12rpx 24rpx;

  .jz-row {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 8rpx;
  }

  .card-content-box {
    display: flex;
    flex-direction: column;
  }

  .field-box {
    flex: 1;
    display: flex;
    font-size: $font-sm;
    .field-label {
      width: 140rpx;
      text-align: right;
    }
    .field-value {
      flex: 1;
      padding-left: 8rpx;
    }
  }

  .bh-list {
    display: flex;
    flex-direction: column;
    font-size: $font-sm;
    .list-item {
      display: flex;
      flex-direction: column;
      .list-item-view {
        display: flex;
        flex-direction: row;
        line-height: 44rpx;
      }
      .list-cell {
        flex: 1;
      }
      .cell-time {
        width: 288rpx;
      }
      .cell-inline {
        max-width: 180px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
    }
  }


  .color-red {
    color: red;
    border: 2rpx solid red;
  }

  .color-black {
    color: #000;
    border: 2rpx solid #000;
  }

  .color-green {
    color: #4cb84b;
    border: 2rpx solid #4cb84b;
  }
}

.com-card-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .com-card-bottom-left {
    .promo-date {
      //color: $uni-text-color-grey;
      //font-size: $uni-font-size-sm;
    }
  }

  .com-card-bottom-right {
    .com-button {

    }
  }
}

.jz-button {
  font-size: $font-sm;
  padding: 12rpx 12rpx;
  display: flex;
  .jz-button-text {
    align-items: center;
    justify-content: center;
    padding-left: 8rpx;
  }
}

.jz-button-add {
  background-color: $primary-color-light;
}
.jz-button-remove {
  background-color: $primary-error-base;
}

.disabled {
  background-color: $font-color-disabled;
}

.font-red {
  color: red;
}
</style>
