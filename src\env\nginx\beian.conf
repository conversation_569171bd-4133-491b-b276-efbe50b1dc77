server {
	listen       80;
	server_name  www.qyqy268.com qyqy268.com;
	root /etc/nginx/beian/qyqy268com/;
}

server {
	listen       80;
	server_name  www.qyqy268268.com qyqy268268.com;
	root /etc/nginx/beian/qyqy268268com/;
}

server {
	listen       80;
	server_name  www.a380a.com a380a.com;
	root /etc/nginx/beian/a380a/;
}

server {
	listen       80;
	server_name  www.aa502.cn aa502.cn;
	root /etc/nginx/beian/aa502/;
}

server {
	listen       80;
	server_name  www.yzlywin08.com yzlywin08.com;
	root /etc/nginx/beian/yzlywin08/;
}

server {
	listen       80;
	server_name  www.3322a.cn 3322a.cn;
	root /etc/nginx/beian/3322a/;
}

server {
	listen       80;
	server_name  www.t365t.net t365t.net;
	
	root /etc/nginx/beian/t365t/;
	
	#location ~* ^/wechat {
	#	root /etc/nginx;
	#	index  index.html index.htm;
	#	proxy_set_header  X-Real-IP  $remote_addr;
	#	proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
	#	proxy_set_header  Host $http_host;
	#	proxy_read_timeout 300;
	#}
}