const fs = require('fs');
const params = process.argv;
let fileEnv = 'local', h5Flag = false, tenantId, secondPath, lastSecondPath;
if(params.length > 1) {
    fileEnv = params[2];
    tenantId = params[3];
    h5Flag = params[4];
    secondPath = params[5];
    lastSecondPath = params[6];
}


let manifest = fs.readFileSync(`${process.cwd()}/src/env/manifest/manifest-local.json`, 'utf8');
const pages = require('./pages/pages-'+fileEnv+'.json');
let website = fs.readFileSync(`${process.cwd()}/src/env/params/website-local.js`, 'utf8');
let runParams = fs.readFileSync(`${process.cwd()}/src/env/params/params-local.env`, 'utf8');
const style = fs.readFileSync(`${process.cwd()}/src/env/scss/style-` + fileEnv + `.scss`, 'utf8');

if (h5Flag === '1') {
    runParams += '\r\nVUE_APP_IS_H5 = 1';
    console.log(runParams)
}
console.log('secondPath=', h5Flag, secondPath, lastSecondPath)
manifest = manifest.replace(/#wechat#/g, secondPath);
website = website.replace(/#wechat#/g, secondPath);

fs.writeFile(
    `${process.cwd()}/.env`,
    runParams,
    e => (e ? console.error(e) : console.log('.env 更新为' + runParams))
);

fs.writeFile(
    `${process.cwd()}/src/pages.json`,
    JSON.stringify(pages),
    e => (e ? console.error(e) : console.log('pages.json 更新为' + fileEnv))
);

fs.writeFile(
    `${process.cwd()}/src/manifest.json`,
    manifest,
    e => (e ? console.error(e) : console.log('manifest.json 更新为' + fileEnv))
);

fs.writeFile(
    `${process.cwd()}/src/common/website.js`,
    website,
    e => (e ? console.error(e) : console.log('.website 更新为' + fileEnv))
);

fs.writeFile(
    `${process.cwd()}/src/uni.scss`,
    style,
    e => (e ? console.error(e) : console.log('style.scss 更新为' + fileEnv))
);

fs.writeFile(
    `${process.cwd()}/build.properties`,
    runParams+ "\nWEBSITE_PATH=" + secondPath + "\nLAST_WEBSITE_PATH=" + lastSecondPath,
    e => (e ? console.error(e) : console.log('build.properties 更新为' + secondPath))
);