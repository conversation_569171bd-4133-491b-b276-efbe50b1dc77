<template>
  <view class="content">
    <scroll-view class="scroll-box" scroll-y>
      <view class="board-table">
        <view class="board-item" v-for="(value, key) in boardData" :key="key" @click="boardClick(key, value)">{{key}}: {{value}}</view>
      </view>
      <view class="m-t">
        <view>
          <com-backup-user :item="item" v-for="(item, index) in pageInfo.list" :key="index" @afterChange="afterChange"></com-backup-user>
        </view>
      </view>
      <uni-load-more v-if="pageInfo.params.size > 0 && pageInfo.list.length > 0" :status="pageInfo.loadStatus"></uni-load-more>
      <uni-popup ref="popup" type="dialog">
        <uni-popup-dialog ref="inputClose" mode="input" :title="changeData.key" :beforeClose="true" @close="close" @confirm="confirm">
          <template slot="default">
            <view class="num-box">
              <uni-number-box :min="0" :max="9999999999" :step="100" v-model="changeData.value"></uni-number-box>
            </view>
          </template>
        </uni-popup-dialog>
      </uni-popup>
    </scroll-view>
  </view>
</template>

<script>
import {isEmpty} from "@/util/validate";
import uniLoadMore from "@/components/uni-load-more";
import ComBackupUser from "@/pages/admin/components/com-backup-user.vue";
import {changeDashboardData, getBackupUsers, getDashboard} from "@/common/request/admin";
import UniPopup from "@/components/uni-popup/uni-popup.vue";
import UniNumberBox from "@/components/uni-number-box.vue";
import UniPopupDialog from "@/components/uni-popup-dialog.vue";

export default {
  name: "pay-backup-users",
  components: {UniPopupDialog, UniNumberBox, UniPopup, ComBackupUser,uniLoadMore},
  data() {
    return {
      pageInfo: {
        list: [],
        loadStatus: 'more',
        params: {
          current: 0,
          size: 100,
        },
      },
      boardData: {},
      changeData: {
        key: "",
        value: 0,
      }
    }
  },
  onLoad() {
    // let options = this.getParams();
    // console.log('backup onShow', options);
    this.getBackupUsers();
    this.getDashboard();
  },
  methods: {
    getDashboard() {
      getDashboard().then(res => {
        this.boardData = res.data;
      })
    },
    getBackupUsers(nextPage, userId, tenantId) {
      if(nextPage) {
        this.pageInfo.params.current += 1;
      } else {
        if (userId) {
          this.pageInfo.params.userId = userId;
          this.pageInfo.params.tenantId = tenantId;
        } else {
          this.pageInfo.loadStatus = 'more';
          this.pageInfo.list = [];
          this.pageInfo.params.current = 1;
          this.pageInfo.params.userId = undefined;
          this.pageInfo.params.tenantId = undefined;
        }
      }
      if(this.pageInfo.loadStatus !== 'more') {
        return;
      }
      uni.showLoading();
      getBackupUsers(this.pageInfo.params).then(res=> {
        const data = res.data.records;
        if(!isEmpty(data)) {
          if (userId) {
            for (let item of this.pageInfo.list) {
              if (item.userId === userId) {
                // console.log('getBackupUsers', data[0], item)
                item.backup = data[0].backup;
              }
            }
          } else {
            this.pageInfo.list = this.pageInfo.list.concat(data);
          }
        }
        if(!data || data.length < this.pageInfo.size) {
          this.pageInfo.loadStatus = 'noMore'
        }
      }).finally(()=> {uni.hideLoading();});
    },
    afterChange(userId, tenantId) {
      this.getBackupUsers(false, userId, tenantId);
    },

    openPopup() {
      this.$refs.popup.open()
    },
    close() {
      this.$refs.popup.close()
    },
    boardClick(key, value) {
      this.changeData.key = key;
      this.changeData.value = value;
      this.openPopup();
    },
    confirm() {
      changeDashboardData(this.changeData).then(res => {
        this.getDashboard();
        this.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
page,
.content {
  height: 100%;
  background-color: $page-color-base;
  display: flex;
  flex-direction: column;
}
.board-table{
  display: flex;
  flex-wrap: wrap;
  margin-top: 24rpx;
  .board-item {
    width: 50%;
    align-items: center;
    justify-content: center;
    padding: 12rpx 24rpx;
  }
}
</style>