<template>
    <uni-popup ref="popup" type="dialog">
        <uni-popup-dialog ref="inputClose" mode="input" :title="title" :beforeClose="true"
                          @close="close" @confirm="confirm">
            <template slot="default">
                <view class="num-box">
                    <uni-number-box :min="1" :max="9999999999" :step="1" v-model="chargeCoinQty"></uni-number-box>
                </view>
            </template>
        </uni-popup-dialog>
    </uni-popup>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup'
import uniPopupDialog from '@/components/uni-popup-dialog'
import uniNumberBox from '@/components/uni-number-box'
import {showMsg} from "@/common/jz-util";
import {submitCoin} from '@/common/request/order'

export default {
    name: "charge",
    components: {uniPopup, uniPopupDialog, uniNumberBox},
    props: {
        // 每列显示个数
        coin: {
            type: Number,
            default: function(){
                return 100;
            }
        },
        gameId: '',
        title: {
            type: String,
            default: function () {
                return '充值金币'
            }
        },
    },
    data() {
        return {
            chargeCoinQty: 100,
        }
    },
    onLoad() {
        this.chargeCoinQty = this.coin;
    },
    methods: {
        openPopup() {
            this.$refs.popup.open()
        },
        close() {
            this.$refs.popup.close()
        },
        confirm() {
            if(!this.chargeCoinQty) {
                showMsg('金币应大于1');
                return;
            }
            submitCoin({coin: this.chargeCoinQty}).then(res=> {
                let orderPayDto = res.data;
                if(this.gameId) {
                    orderPayDto.productId = orderPayDto;
                }
                this.$refs.popup.close()
                console.log('submitCoin', orderPayDto);
                if (orderPayDto.tenantId && orderPayDto.redirect) {
                  window.location.href = orderPayDto.redirect + "/wechat/wxd19c0f89a350beaa/pay/backup?orderId=" + orderPayDto.orderId;
                } else {
                  console.log('before gotoName', orderPayDto);
                  this.gotoName('pay', orderPayDto);
                }
            }).catch(res=>{});
        }
    }
}
</script>

<style lang="scss" scoped>
.num-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    .uni-numbox {
        width: 90%;
    }
}
.uni-popup__info {
    color: #000000;
}
</style>
