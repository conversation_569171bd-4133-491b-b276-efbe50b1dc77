<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getHistoryGameList(true)">
            <view class="series">
                <view class="series-memo-box">
                    <view class="series-memo-img" align="center">
                        <image :src="game.image|productImg" mode="widthFix"/>
                    </view>
                    <view class="series-memo-box-r">
                        <view class="series-title-box">
                            <text class="series-title">{{game.name}}</text>
<!--                            <text class="series-title-memo">知名美女主播</text>-->
                        </view>
                        <view class="series-memo">
                            {{game.promotion}}
                        </view>
                    </view>
                </view>
            </view>
            <view class="m-t">
                <uni-section title="最近成绩" type="line"></uni-section>
                <view>
                    <com-promo-series-history :series-id="promoSeriesId"></com-promo-series-history>
                </view>
            </view>
            <view class="m-t">
                <uni-section title="最新方案" type="line"></uni-section>
                <view>
                    <com-promo :item="item" v-for="(item, index) in newPromoList" :key="index" @buyGame="buy"></com-promo>
                </view>
            </view>
            <view class="m-t">
                <uni-section title="历史方案" type="line"></uni-section>
                <view class="promo-list">
                    <com-promo :dateFlag="true" :item="item" v-for="(item, index) in historyPromoList" :key="index"></com-promo>
                </view>
            </view>
            <charge ref="popup" title="余额不足，请及时充值"></charge>
            <uni-load-more v-if="pageInfo.size > 0 && historyPromoList.length > 0" :status="loadStatus"></uni-load-more>
        </scroll-view>
        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from './components/com-promo'
import uniFab from '@/components/uni-fab'
import comPromoSeriesHistory from './components/com-promo-series-history'
import {getGameItem,getTodayGameList,getHistoryGameList,getSeriesId} from "@/common/request/promo-series";
import uniLoadMore from "@/components/uni-load-more";
import {isEmpty} from "@/util/validate";
import promoBuy from "@/pages/home/<USER>/promo-buy";
import charge from "@/pages/customer/components/charge";

export default {
    components: {uniSection, uniCard, comPromo, uniFab, uniLoadMore, comPromoSeriesHistory,charge},
    mixins: [promoBuy],
    data() {
        return {
            promoSeriesId: '',
            game: {},
            newPromoList: [],
            historyPromoList: [],
            items: [],
            loadStatus: 'more',
            pageInfo: {
                current: 0,
                size: 8,
            },
        }
    },
    onLoad() {
        let options = this.getParams();
        console.log('game onload', options);
        this.promoSeriesId = options.promoSeriesId;
        if(this.promoSeriesId) {
            this.getSeriesById();
            this.getTodayGameList();
            this.getHistoryGameList();
        }
    },
    methods: {
        getSeriesById() {
            getSeriesId(this.promoSeriesId).then(res=> {
                const series = res.data;
                this.game = series;
            }).catch(res=>{});
        },
        getTodayGameList() {
            getTodayGameList({promoSeriesId: this.promoSeriesId,noResult:false}).then(res=> {
                this.newPromoList = res.data;
            }).catch(res=>{});
        },
        getHistoryGameList(nextPage) {
            if(nextPage) {
                this.pageInfo.current += 1;
            } else {
                this.loadStatus = 'more';
                this.historyPromoList = [];
                this.pageInfo.current = 1;
            }
            if(this.loadStatus !== 'more') {
                return;
            }
            this.pageInfo.promoSeriesId = this.promoSeriesId;
            uni.showLoading();
            getHistoryGameList(this.pageInfo).then(res=> {
                const data = res.data.records;
                if(!isEmpty(data)) {
                    this.historyPromoList = this.historyPromoList.concat(data);
                }
                if(!data || data.length < this.pageInfo.size) {
                    this.loadStatus = 'noMore'
                }
                uni.hideLoading();
            }).catch(res=>{uni.hideLoading()});
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
}
.series {
    background-color: #fff;
    padding: 10rpx 24rpx 12rpx 14rpx;
    .top-title {
        font-size: $font-lg + 4rpx;
        padding-left: 12rpx;
    }
    .series-memo-box {
        display: flex;
        flex-direction: row;
        padding: 20rpx 12rpx 10rpx 20rpx;
        .series-memo-img {
            image {
                width: 160rpx;
                height: 160rpx;
            }
        }
        .series-memo-box-r {
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            .series-title-box {
                padding: 0 0 12rpx 0;
                .series-title {
                    font-size: $font-lg;
                    font-weight: 700;
                }
                .series-title-memo {
                    font-size: $font-sm;
                    padding-left: 20rpx;
                }
            }
            .series-memo {
                font-size: $font-base;
                text-indent: 2em;
                color: $font-color-light;
            }
        }
    }
}
.promo-reason {
    background-color: #fff;
    .promo-content {
        padding: 10rpx 24rpx;
        font-size: $font-lg;
        background-color: #fff;
        text-indent: 2em;
        color: $font-color-light;
    }
}
</style>
