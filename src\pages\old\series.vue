<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getData(true)">
            <view class="promo-list">
                <com-promo :dateFlag="true" :item="item" v-for="(item, index) in pageInfo.list" :key="index"></com-promo>
            </view>
            <uni-load-more v-if="pageInfo.params.size > 0 && pageInfo.list.length > 0" :status="pageInfo.loadStatus"></uni-load-more>
            <jz-empty v-if="!pageInfo.list || pageInfo.list.length === 0" content="暂无数据"></jz-empty>
        </scroll-view>
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from './components/com-promo'
import uniFab from '@/components/uni-fab'
import {
    getGameItem,
    getTodayGameList,
    getHistoryGameList,
    getGameResultText
} from "@/common/request/promo-series";
import uniLoadMore from "@/components/uni-load-more";
import {isEmpty} from "@/util/validate";
import promoBuy from "@/pages/home/<USER>/promo-buy";
import charge from "@/pages/customer/components/charge";
import ComPromoSeries from "@/pages/home/<USER>/com-promo-series";
import ComPromoSeriesHead from './components/com-promo-series-head';
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";
import jzEmpty from '@/components/jz-empty';
import {getBalanceHistory} from "@/common/request/customer";
import {getUserGameList} from "@/common/request/user-game";

export default {
    components: {ComPromoSeries, uniSection, uniCard, comPromo, uniFab, uniLoadMore, charge, ComPromoSeriesHead, jzEmpty},
    mixins: [promoBuy],
    data() {
        return {
            rawPay: true,
            results: [],
            promoSeriesId: '',
            game: {},
            newPromoList: [],
            historyPromoList: [],
            items: [],
            pageInfo: {
                list: [],
                loadStatus: 'more',
                params: {
                    current: 0,
                    size: 8,
                },
            }
        }
    },
    onShow() {
        let options = this.getParams();
        console.log('game onload', options);
        this.promoSeriesId = options.id;
        if(this.promoSeriesId) {
            this.getTodayGameList();
            this.getData();
        }
    },
    methods: {
        getTodayGameList() {
            getTodayGameList({promoSeriesId: this.promoSeriesId,noResult:false}).then(res=> {
                this.newPromoList = res.data;
            }).catch(res=>{});
        },
        getData(nextPage) {
            this.pageInfo.params.promoSeriesId = this.promoSeriesId;
            bindScrollPageInfo(this.pageInfo, nextPage).then(pageInfo => {
                this.pageInfo = pageInfo;
                getHistoryGameList(this.pageInfo.params).then(res=> {
                    const data = res.data.records;
                    this.pageInfo = bindPageInfo(this.pageInfo, data)
                }).catch(res=>{});
            }).catch(()=> {})
        },
        getResults() {
            const me = this;
            getGameResultText(me.promoSeriesId).then(res=> {
                if(res.data) {
                    me.results = res.data.split('');
                    let times = 0;
                    me.winHistory.times = me.results.length;
                    for(let i = 0; i < me.results.length; i++) {
                        if(this.results[i]==='W') {
                            times++;
                            me.winHistory.winTimes++;
                        }
                    }
                    if(times > 0) {
                        me.winTimes = times+'连胜';
                    }
                }
            }).catch(res=>{});
        },
    }
}
</script>

<style lang="scss">
.content {
    background-color: #FFFFFF;
}
.scroll-box {
    height: 100%;
}
.com-promo:last-child {
    border-bottom: none;
}
.promo-list {
    padding-bottom: 16rpx;
}
</style>
