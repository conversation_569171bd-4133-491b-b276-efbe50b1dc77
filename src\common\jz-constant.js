/**
 * 环境信息
 */
let configTemp = {
	currentEnv: process.env.NODE_ENV,	//当前环境
	appId: process.env.VUE_APP_ID || 'wx446892914899e662',
	tenantId: process.env.VUE_APP_TENANT_ID || '3',
	restUrl: process.env.VUE_APP_REST_URL || 'https://www.yzlywin08.com',
	restPathPrefix: process.env.VUE_APP_REST_PATH_PREFIX || 'football',
	isH5: process.env.VUE_APP_IS_H5 === '1',
	version: '1'
};
if (process.env.NODE_ENV === 'development') {
	console.log('env-development');
	//configTemp.restUrl = 'https://pay.yj-star.com';			//测试环境
	configTemp.log = "https://pay.yj-star.com/es/ylh-mini-dev-log/errorLog";//日志上报
}else if (process.env.NODE_ENV === 'production') {
	console.log('env-production');
	//configTemp.appId = 'wx7125bd357001d576';
	//configTemp.restUrl = 'https://www.yzlywin08.com'; 			//生产环境
	configTemp.log = "https://www.yzlywin08.com/es/ylh-mini-production-log/errorLog";//日志上报
}
console.log( process.env)
export const config = configTemp;

/**
 * 主页导航栏地址
 */
export const TabBarUrl = {
    tabOne: '/pages/apt/index',tabTwo: '/pages/activity/mkt-index',tabThree: '/pages/cart/index',
	tabFour: '/pages/customer/index'
};
/**
 * 用户缓存信息
 */
export const InfoLoginUser = {
	USER_INFO: 'jz-ylh-user',
	HTTP_HEADER_TOKEN: 'token',
	REFERRER_CUST_ID: 'referrerCustId',//分享到人
	REFERRER_CUST_SEQ: 'refCustSeq'//分享朋友圈
};
/**
 * 广播key值
 */
export const EmitKey = {
	LOGIN_SUCCESS_TARGET_URL: 'login_success_target_url',
	APT_SHOP_CHOOSE_CHANGED: 'apt_shop_choose_changed',
	CUST_SHOP_CHANGED: 'cust_shop_changed',
	QR_CODE_REDIRECT_PARAMS: 'qr_code_redirect_params',	//二维码参数
};
/**
 * 客户级别
 */
export const CustomerLevel = {
    1: '大众会员',2: '银卡会员', 3: '黄金会员', 4: '白金会员', 5: '钻石会员'
};

/**
 * 消费订单状态
 */
export const PurchaseStatus = {
	NEW: '新建', NO_PAY: '待付款', NO_ALL_PAY: '待补款', DOWN_PAY: '已下定',
	WAIT_AUDIT: '待审核', IS_CONFIRM: '已确认', IS_COMPLETE: '已完成',
	IS_CANCEL: '已取消', IS_REVOKE : '已撤销'
};

/**
 * 消耗订单状态
 */
export const ConsumeStatus = {
	NEW: '新建', NO_CONFIRM : '待确认', IS_COMPLETE: '已完成', IS_CANCEL: '已作废', IS_REVOKE: '已撤销'
};

/**
 * 消费订单-subType
 */
export const PurchaseSubType = {
	SHOP: '门店', ONLINE: '商城', GUIDE: '导购', CHANGE: '兑换',
	GIFT: '体验券', KS: '蔻匙', KS_ONLINE: '蔻匙线上'
};

/**
 * 服务订单-subType
 */
export const ConsumeSubType = {
	SHOP: '门店', ONLINE : '商城', KS: '蔻匙'
};

/**
 * 服务订单-是否需要服务
 */
export const RatingStatus = {
	NEED_RATE: '待评价', IS_RATE: '已评价', NOT_RATE: '无需评价', DUE_RATE: '超期未平'
}

/**
 * 预约
 */
export const AppointmentStatus = {
	CONFIRMED:'已确认', ARRIVED:'已到店', TO_CONFIRM:'待确认',
	SHOP_CANCEL:'门店取消', CUSTOMER_CANCEL:'客户取消'
}

/**
 * 活动列表
 */
export const SharePageList = {
	//老带新新客页面
	REBATE_202004: {
		page: 'pages/activity/rebate/rebate-receive',
		options: '',
		title: '新人专享礼',
		bgUrl: config.qnBrandImg + 'REBATE_202004.jpg'
	}
}
















