server
{
    listen 443;
    server_name mgr.peetpay.yj-star.com;
    index index.html index.htm ;
    root /home/<USER>/jeepay/mgr;

    #解决vue刷新404问题
    try_files $uri $uri/ /index.html;

	ssl_certificate      peetpay.yj-star.com.cer;
	ssl_certificate_key  peetpay.yj-star.com.key;

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;
	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	gzip on;
	gzip_static on;
	gzip_min_length 1k;
	gzip_comp_level 4;
	gzip_proxied any;
	gzip_types text/plain text/xml text/css application/javascript;
	gzip_vary on;
	gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
    location /api/
    {
        proxy_next_upstream http_502 http_504 error timeout invalid_header;
        proxy_set_header Host  $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://mgr-api;
    }

}
server
{
    listen 443;
    server_name mch.peetpay.yj-star.com;
    index index.html index.htm ;
    root /home/<USER>/jeepay/mch;

    #解决vue刷新404问题
    try_files $uri $uri/ /index.html;

	ssl_certificate      peetpay.yj-star.com.cer;
	ssl_certificate_key  peetpay.yj-star.com.key;

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;
	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	gzip on;
	gzip_static on;
	gzip_min_length 1k;
	gzip_comp_level 4;
	gzip_proxied any;
	gzip_types text/plain text/xml text/css application/javascript;
	gzip_vary on;
	gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
    location /api/
    {
        proxy_next_upstream http_502 http_504 error timeout invalid_header;
        proxy_set_header Host  $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://mch-api;
        # 启用支持websocket连接
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

}

server
{
    listen 443;
    server_name pm.peetpay.yj-star.com;
    index index.html index.htm ;
    root /home/<USER>/jeepay/payment;

    #解决vue刷新404问题
    try_files $uri $uri/ /index.html;

	ssl_certificate      peetpay.yj-star.com.cer;
	ssl_certificate_key  peetpay.yj-star.com.key;

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;
	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	gzip on;
	gzip_static on;
	gzip_min_length 1k;
	gzip_comp_level 4;
	gzip_proxied any;
	gzip_types text/plain text/xml text/css application/javascript;
	gzip_vary on;
	gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
    location /api/
    {
        proxy_next_upstream http_502 http_504 error timeout invalid_header;
        proxy_set_header Host  $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://pm-api;
        # 启用支持websocket连接
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

}