<template>
    <view class="content">
    </view>
</template>

<script>
import wechatPay from "@/pages/order/wechat-pay";
import {config} from "@/common/jz-constant";
import {getBackupPayInfo} from "@/common/request/order";

export default {
    components: {},
    mixins: [wechatPay],
    onLoad() {
        this.params = this.getParams();
        if (this.params.orderId) {
          getBackupPayInfo(this.params.orderId, this.params.originTenantId).then(res => {
            this.orderPayInfo = res.data;
            this.orderPayInfo.rtPath = decodeURIComponent(this.params.rt);
            const urlObj = new URL(this.orderPayInfo.rtPath);
            this.orderPayInfo.rtDomain = urlObj.origin;
            console.log(urlObj, this.orderPayInfo)
            this.xxxPay();
          }).catch(res => {});
        }
    }
}
</script>

<style scoped>

</style>
