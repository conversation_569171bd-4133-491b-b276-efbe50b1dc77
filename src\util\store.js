import { isEmpty } from '@/util/validate'
import website from '@/common/website'

const keyName = website.siteName + '-'
/**
 * 存储localStorage
 */
export const setStore = (params = {}) => {
  let {
    name,
    content,
    type
  } = params
  name = keyName + name
  const obj = {
    dataType: typeof (content),
    content: content,
    type: type,
    datetime: new Date().getTime()
  }
  console.log('setStorageSync ' + name, obj)
  uni.setStorageSync(name, obj);
}
/**
 * 获取localStorage
 */

export const getStore = (params = {}) => {
  let {
    name,
    debug
  } = params
  name = keyName + name
  let obj = {}
  let content
  obj = uni.getStorageSync(name);
  if (isEmpty(obj)) return
  if (obj.dataType === 'string') {
    content = obj.content
  } else if (obj.dataType === 'number') {
    content = Number(obj.content)
  } else if (obj.dataType === 'boolean') {
    content = eval(obj.content)
  } else if (obj.dataType === 'object') {
    content = obj.content
  }
  return content
}
/**
 * 删除localStorage
 */
export const removeStore = (params = {}) => {
  let {
    name
  } = params
  name = keyName + name
  uni.removeStorageSync(name)
}

/**
 * 清空全部localStorage
 */
export const clearStore = (params = {}) => {
  uni.clearStorageSync();
}
