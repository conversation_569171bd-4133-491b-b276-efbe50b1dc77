#2180=yj
#2080=jm
upstream stream_a {
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_b {
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_c {
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_d {
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_e {
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_f {
	#server localhost:4444;
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_h {
	server localhost:2080;
}
upstream stream_i {
	#server localhost:4444;
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_x {
	#server localhost:4444;
	server *********:9999;
	server *********:9999 backup;
	#server localhost:2180 max_fails=10 fail_timeout=1h;
	#server localhost:2080 max_fails=10 fail_timeout=1h backup;
}
upstream stream_gray {
	server *********:9999;
}




upstream stream_g {
	server *********:9999;
	server *********:9999 backup;
}
upstream stream_j {
	server *********:9999;
	server *********:9999 backup;
}
upstream stream_cloud_a {
	server *********:9999;
	server *********:9999 backup;
}
upstream stream_oss{
	server *********:9999;
	server *********:9999 backup;
}

upstream stream_pay_yjstar {
	server *********:9999;
	#server *********:9999 backup;
}


upstream mch-api {
	#weight=1 max_fails=2 fail_timeout=30s;
    server *********:9218;
	server *********:9218 backup;
}
upstream pm-api {
    server *********:9216;
    server *********:9216 backup;
}
upstream mgr-api {
    server *********:9217;
    server *********:9217 backup;
}



