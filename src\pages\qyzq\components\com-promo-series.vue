<template>
  <uni-card isShadow :note="item.priceDay" @click="goto('/series', {id: item.id})">
    <template v-slot:header>
      <view class="com-card-title">
        <view class="title-left">
          <view class="game-index gradient">
            {{ index+1 }}
          </view>
          <view class="game-image">
            <img :src="$website.ossDomain + item.image" mode="aspectFill" :lazy-load="true"></img>
          </view>
          <view class="game-name">
            <view class="game-name-text">
              <view>{{ item.name }}</view>
              <view v-if="item.followId!==undefined">
                <!--                                <uni-fav :checked="item.followId" icon="icon-jia" class="fav" :content-text="{contentDefault:'关注',contentFav:'已关注'}" @click.stop="followGame(item.id)" />-->
                <view v-if="!item.followId" class="jz-button full-border" @click.stop="followGame(item.id)">
                  <text class="ylh-icon icon-jia"></text>
                  <text class="jz-button-text">关注</text>
                </view>
                <view v-else class="jz-button full-border" @click.stop="unFollowGame(item.id)">
                  <text class="ylh-icon icon-gouxuan"></text>
                  <text class="jz-button-text">已关注</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="title-right" v-if="rateItem.rateText">
          <text class="title-win-rate font-red">{{ rateItem.rate }}%</text>
          <text class="title-win-type">
            {{ rateItem.rateText }}
          </text>
        </view>
      </view>
    </template>
    <view class="com-card-content">
      <view class="jz-row">
        <view class="card-content-box">
          <view class="game-tag-box">
            <text class="game-tag m-r" :class="{'blink y-chul':item.hasPromotion, 'w-chul': !item.hasPromotion}">
              {{ item.hasPromotion === true ? '已出料' : '未出料' }}
            </text>
            <!--<text class="game-tag m-r" v-if="!canBuy(item)">￥{{item.priceDay}}</text>-->
            <!--<text class="game-tag m-r">周胜率 100%</text>-->
            <text class="game-tag m-r" v-if="winTimes">{{ winTimes }}</text>
            <text class="x-month" v-if="item.lastGamePromo && includeMonth">{{ includeMonth }}</text>
            <text class="x-month-test" v-if="item.lastGamePromo && includeMonth">收录{{ includeMonth }}月</text>
          </view>
          <com-promo-win-times :results="results"></com-promo-win-times>
        </view>
      </view>
      <view class="jz-row" v-if="item.lastGamePromo">
        <view class="game-box">
          <view class="game-box-content">
            <view class="game-play-time">
              <text>{{ item.lastGamePromo.gameName }}</text>
              <text> {{ ' ' }}| {{ item.lastGamePromo.playTime | date('MM-dd hh:mm') }}</text>
              <text class="game-hot" v-if="item.gameHot === true">
                <text class="ylh-icon icon-jisubangdan"></text>
                热门
              </text>
            </view>
            <view class="game-home-away">
              <text class="game-home deg-box">{{ item.lastGamePromo.home }}</text>
              <text class="game-vs">VS</text>
              <text class="game-away deg-box">{{ item.lastGamePromo.away }}</text>
            </view>
            <view class="game-score m-t" v-if="item.lastGamePromo.score">
              比分：{{ item.lastGamePromo.score }}
            </view>
          </view>
        </view>
      </view>

    </view>
    <template v-slot:footer>
      <view class="com-card-bottom">
        <view class="jz-button" :class="{'bg-view':!canBuy(item)}"
              @click.stop="goto('/game', {id: item.lastGamePromo.id ,promoSeriesId: item.id})">
          <!--                    <block v-if="item.priceDay && canBuy(item)">-->
          <text class="ylh-icon icon-coin"></text>
          <text class="jz-button-text">{{ item.priceDay }} {{ canBuy(item) ? '订阅' : '单场' }}</text>
          <!--                    </block>-->
          <!--                    <text class="jz-button-text" v-else>查看赛事</text>-->
        </view>
        <!--<view class="com-card-bottom-left">-->
        <!--</view>-->
        <!--<view class="com-card-bottom-right">-->
        <!--</view>-->

      </view>
    </template>
  </uni-card>
</template>

<script>
import UniGrid from '@/components/uni-grid/uni-grid'
import UniGridItem from '@/components/uni-grid/uni-grid-item'
import UniCard from '@/components/uni-card'
import uniLoadMore from '@/components/uni-load-more'
import jzEmpty from '@/components/jz-empty';
import {buyGame} from '@/common/request/order'
import {isEmpty} from "@/util/validate";
import {followGame, unFollowGame, getGameResultText} from '@/common/request/promo-series'
import {showMsgConfirm} from "@/common/jz-util"
import ComPromoWinTimes from "@/pages/qyzq/components/com-promo-win-times";
// import UniFav from '@/components/uni-fav'
import UniBadge from '@/components/uni-badge';

export default {
  name: "comPromoSeries",
  components: {ComPromoWinTimes, UniGrid, UniGridItem, UniCard, uniLoadMore, jzEmpty, UniBadge},
  props: {
    index: {
      type: Number,
      default: function () {
        return -1;
      }
    },
    item: {
      type: Object,
      default: function () {
        return {};
      }
    },
    weekRate: {
      type: Boolean,
      default: function () {
        return true;
      }
    }
  },
  filters: {},
  data() {
    return {
      results: [],
      winTimes: '',
      includeMonth: 0,
      rateItem: {
        rate: undefined,
        rateText: undefined
      }
    }
  },
  mounted() {
    if (this.item.id) {
      this.getResults();
      this.setRateItem();
      this.includeMonth = this.monthsBetween(this.item.includeDate);
    }
  },
  methods: {
    canBuy(item) {
      const gamePromo = item.lastGamePromo;

      //没有赛事不能买
      if (gamePromo == null) {
        return false;
      }
      //未购买过
      const isNotBuy = isEmpty(item.buyTime);

      // if(item.type === '篮球' && isNotBuy) {
      //     return true;
      // }

      //未到开始时间
      let gameNotStart = false;
      if (gamePromo.playTime) {
        let now = new Date();
        //console.log('canBuy', gamePromo.playTime)
        let playTime = new Date(gamePromo.playTime.replace(/-/g, "/"));
        playTime = new Date(playTime.setHours(playTime.getHours() + 2))
        gameNotStart = playTime > now;
      }

      //已出料 && 未到开始时间 && 未发布结果 && 未购买过
      return item.hasPromotion && gameNotStart && isEmpty(gamePromo.result) && isNotBuy
    },
    buy(game) {
      if (this.canBuy(game)) {
        this.$emit('buyGame', game);
      }
    },
    getCombineGames(games) {
      const gameArray = games.split('\n');
      let combineGamesList = [];
      for (let game of gameArray) {
        const gameContentArray = game.split(",");
        if (gameContentArray.length === 3) {
          combineGamesList.push({playTime: gameContentArray[0], home: gameContentArray[1], away: gameContentArray[2]});
        }
      }
      return combineGamesList;
    },
    getResults() {
      const me = this;
      getGameResultText(me.item.id).then(res => {
        if (res.data) {
          me.results = res.data.split('');
          let times = 0;
          let searchFlag = true;
          for (let i = 0; i < this.results.length; i++) {
            if (searchFlag) {
              if (this.results[i] === 'W') {
                times++;
              } else {
                searchFlag = false;
              }
            }

          }
          if (times > 1) {
            me.winTimes = times + '连胜';
          }
        }
      }).catch(res => {
      });
    },
    setRateItem() {
      console.log('setRateItem')
      const promoSeries = this.item;
      if (this.weekRate) {
        if (promoSeries.weekRate != null && promoSeries.weekRate >= 0) {
          this.rateItem.rate = promoSeries.weekRate;
          this.rateItem.rateText = '周胜率';
        }
      } else {
        if (promoSeries.monthRate != null && promoSeries.monthRate >= 0) {
          this.rateItem.rate = promoSeries.monthRate;
          this.rateItem.rateText = '月胜率';
        }
      }
    },
    monthsBetween(date1) {
      //console.log('monthsBetween', date1)
      if (!date1) {
        return 0;
      }
      date1 = date1.split("-");
      const date2 = new Date();
      const year2 = date2.getFullYear();
      const month2 = date2.getMonth() + 1;
      const year1 = parseInt(date1[0]), month1 = parseInt(date1[1]);
      return (year2 - year1) * 12 + (month2 - month1) + 1;
    },
    followGame(promoSeriesId) {
      const me = this;
      followGame({promoSeriesId: promoSeriesId}).then(res => {
        me.item.followId = '1';
      }).catch(res => {
      });
    },
    unFollowGame(promoSeriesId) {
      const me = this;
      unFollowGame(promoSeriesId).then(res => {
        me.item.followId = 0;
      }).catch(res => {
      });
    },
  }
}
</script>

<style lang="scss">
.com-card-title {
  flex: 1;
  display: flex;
  justify-content: space-between;

  .title-left {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    align-items: center;

    .game-index {
      text-align: center;
      color: #fff;
      font-size: $font-base;
      font-weight: bold;
      padding: 4rpx 0;
      border-radius: 20%;
      width: 40rpx;
    }
    .gradient {
      /* 设置容器尺寸 - 原理1 */
      //width: 400px;
      //height: 400px;
      /* 背景渐变色 - 原理2 */
      background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
      /* 背景尺寸 - 原理3 */
      background-size: 200% 200%;
      /* 循环动画 - 原理4 */
      animation: gradientBG 5s ease infinite;
    }

    /* 动画，控制背景 background-position */
    @keyframes gradientBG {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
    .jz-button {
      width: 120rpx;
      margin-left: 20rpx;
      font-size: $font-sm;
      padding: 4rpx;
      background-color: transparent;
      color: $primary-color-base;

      .ylh-icon {
        font-size: $font-base;
      }

      .jz-button-text {
        padding-left: 4rpx;
      }
    }
  }

  .title-right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title-win-rate {
      font-size: $font-base;
      font-weight: 700;
    }

    .title-win-type {
      font-size: $font-sm;
      color: #888888;
    }
  }

  .game-name {
    display: flex;
    flex: auto;
    align-items: center;
    padding-left: 12rpx;

    .game-name-text {
      flex: 1;
      font-size: $font-lg;
      color: #000;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }

  .game-image {
    width: 68rpx;
    height: 68rpx;
    margin-left: 12rpx;

    img {
      width: 100%;
      height: 100%;
    }

    .badge-index {
      position: absolute;
      top: 10upx;
      left: 10upx;
    }

    image {
      //border-radius:50%;
      width: 100%;
      height: 100%;
    }
  }

  .play-time {
    width: 160rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: right;
    color: $font-color-light;
    font-size: $font-sm;
  }
}

.fav {
  margin-left: 20rpx;
}

.com-card-content {
  padding: 12rpx 12rpx 12rpx 24rpx;

  .jz-row {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .game-box {
    margin-right: 14rpx;
    padding-top: 16rpx;

    .game-box-content {
      border-radius: 12rpx;
      //background-color: #cccccc;
      padding: 8rpx 48rpx 16rpx 36rpx;
      box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
    }

    .game-play-time {
      font-size: $font-sm;
      color: #888888;
      padding: 8rpx 4rpx 16rpx 4rpx;

      .game-hot {
        color: #ff0000;
        padding-left: 8rpx;
        font-weight: bold;
      }
    }

    .game-home-away {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;

      .game-play-time {
        font-size: $font-sm;
        padding-right: 12rpx;
      }

      .deg-box {
        position: relative;
        padding: 4rpx 0;
        font-size: $font-sm;
      }

      .deg-box::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: -1;
        transform: skew(-12deg);
      }

      .game-home {
        flex: 1;
        color: #fff;
      }

      .game-home::before {
        background: #0542b1;
      }

      .game-vs {
        width: 100rpx;
      }

      .game-away {
        flex: 1;
        color: #fff;
      }

      .game-away::before {
        background: #ff0000;
      }
    }

    .game-score {
      font-size: $font-sm;
      color: #4a4c55;
      border-radius: 12rpx;
      //background-color: #cccccc;
      padding: 8rpx 48rpx 8rpx 36rpx;
      box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
    }
  }

  .combine-box {
    padding: 24rpx 24rpx 10rpx 12rpx;

    .game-vs {
      width: 80rpx;
    }
  }

  .card-content-box {
    display: flex;
    flex-direction: column;
  }

  .game-left {
    flex: auto;
    display: flex;
    flex-direction: column;
    color: $font-color-base;
    font-size: $font-lg;

    .game-title {
      font-weight: bold;
      padding: 16rpx 4rpx 12rpx 4rpx;
    }

    .game-title-time {
      padding-bottom: 6rpx;
      font-weight: 400;
      color: #000;
      font-size: $font-sm;
    }
  }

  .game-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 120rpx;
    padding-left: 20rpx;
    padding-right: 10rpx;
  }
}

.com-card-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .jz-button {
    font-size: $font-base;
    padding: 8rpx 32rpx;
    color: #ffffff;

    .ylh-icon {
      font-size: $font-base;
    }

    .jz-button-text {
      padding-left: 4rpx;
    }
  }

  .bg-disabled {
    background-color: $primary-color-gray;
  }

  .bg-view {
    background-color: $primary-color-light;
  }

  .com-card-bottom-left {

  }

  .com-card-bottom-right {
    .jz-button {
      padding: 8rpx 36rpx;
    }
  }
}

.jz-button {
  font-size: $font-lg - 4rpx;
  padding: 12rpx 12rpx;
  background-color: $primary-color-light;
}

.disabled {
  background-color: $font-color-disabled;
}

.game-price {
  font-size: $font-base;
}

.game-price-padding {
  padding-left: 24rpx;
}

.font-red {
  color: red;
}

/*.main{
      color: #666;margin-top: 50px;
    }*/
/* 定义keyframe动画，命名为blink */
@keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

/* 添加兼容性前缀 */
@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

@-moz-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

@-ms-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

@-o-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

/* 定义blink类*/
.blink {
  color: #F8F8FF;
  font-weight: bold;
  animation: blink 1s linear infinite;
  /* 其它浏览器兼容性前缀 */
  -webkit-animation: blink 1s linear infinite;
  -moz-animation: blink 1s linear infinite;
  -ms-animation: blink 1s linear infinite;
  -o-animation: blink 1s linear infinite;
}

.game-tag-box {
  font-size: $font-base;

  .game-tag {
    display: inline-block;
    align-items: center;
    justify-content: center;
    background: $primary-color-base;
    color: #ffffff;
    padding: 0 16rpx;
  }

  .y-chul {
    background: red;
  }

  .w-chul {
    background-color: #cccccc;
  }

  .m-r {
    margin-right: 8rpx;
  }

  .x-month {
    display: inline-block;
    color: #ffffff;
    background: #ffa258;
    padding: 0 4rpx;
  }

  .x-month-test {
    display: inline-block;
    color: #ffa258;
    background: #faf5d7;
    padding: 0 12rpx;
  }
}

.game-win-box {
  padding: 14rpx 8rpx 0 0;

  .game-result-icon {
    display: inline-block;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-radius: 50%;
    width: 38rpx;
    height: 38rpx;
    font-size: 22rpx;
    font-weight: 600;
    margin-right: 8rpx;
  }
}

.w-chul {
  display: inline-block;
  background-color: #cccccc;
  font-size: $font-base;
  padding: 0 10px;
}
</style>
