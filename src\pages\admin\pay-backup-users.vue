<template>
  <view class="content">
    <scroll-view class="scroll-box" scroll-y>
      <view class="board-table">
        <view class="board-item" @click="boardClick('tenantId', queryItem.tenantId)">tenantId: {{queryItem.tenantId}}</view>
        <view class="board-item" v-for="(value, key) in boardData" :key="key" @click="boardClick(key, value)">{{key}}: {{value}}</view>
      </view>
      <view class="board-table">
        <view class="board-group" v-for="(item, index) in payHistoryInfo.list" :key="index">
          <view class="board-line">
            <text class="board-line-item">{{item.tenantName}}</text>
            <text class="board-line-item">{{item.nickname}}</text>
            <text class="board-line-item">backup:
              <text :class="{'color-green':item.backup}">{{item.backup|dictSF}}</text>
            </text>
            <text class="board-line-item">payBackup:
              <text :class="{'color-red':item.payBackup}">{{item.payBackup|dictSF}}</text>
            </text>
          </view>
          <view class="board-line">
            <text class="board-line-item">{{item.createdTime| date('MM-dd hh:mm')}}</text>
            <text class="board-line-item">{{item.tenantId}}</text>
            <text class="board-line-item">{{ item.title}}</text>
          </view>
          <view class="board-line">
            <text class="board-line-item color-red">{{item.amount}}</text>
            <text class="board-line-item">{{item.userId}}</text>
          </view>
        </view>
      </view>
      <view class="m-t">
        <view>
          <com-backup-user :item="item" v-for="(item, index) in pageInfo.list" :key="index" @afterChange="afterChange"></com-backup-user>
        </view>
      </view>
      <uni-load-more v-if="pageInfo.params.size > 0 && pageInfo.list.length > 0" :status="pageInfo.loadStatus"></uni-load-more>
      <uni-popup ref="popup" type="dialog">
        <uni-popup-dialog ref="inputClose" mode="input" :title="changeData.key" :beforeClose="true" @close="close" @confirm="confirm">
          <template slot="default">
            <view class="num-box">
              <uni-number-box :min="0" :max="9999999999" :step="100" v-model="changeData.value"></uni-number-box>
            </view>
          </template>
        </uni-popup-dialog>
      </uni-popup>
    </scroll-view>
  </view>
</template>

<script>
import {isEmpty} from "@/util/validate";
import uniLoadMore from "@/components/uni-load-more";
import ComBackupUser from "@/pages/admin/components/com-backup-user.vue";
import {
  changeDashboardData,
  changePayTenantId,
  getBackupUsers,
  getDashboard,
  getPayHistoryList
} from "@/common/request/admin";
import UniPopup from "@/components/uni-popup/uni-popup.vue";
import UniNumberBox from "@/components/uni-number-box.vue";
import UniPopupDialog from "@/components/uni-popup-dialog.vue";

export default {
  name: "pay-backup-users",
  components: {UniPopupDialog, UniNumberBox, UniPopup, ComBackupUser,uniLoadMore},
  data() {
    return {
      pageInfo: {
        list: [],
        loadStatus: 'more',
        params: {
          current: 0,
          size: 50,
        },
      },
      queryItem: {
        userId: undefined,
        tenantId: 0
      },
      boardData: {},
      changeData: {
        key: "",
        value: 0,
      },
      payHistoryInfo: {
        list: [],
        params: {
          current: 0,
          size: 20,
          userId: undefined,
          tenantId: 0
        }
      }
    }
  },
  onLoad() {
    // let options = this.getParams();
    // console.log('backup onShow', options);
    this.getBackupUsers();
    this.getDashboard();
  },
  onShow() {
    this.getPayHistoryList();
  },
  methods: {
    dictSF() {
      return dictSF
    },
    getDashboard() {
      getDashboard().then(res => {
        this.boardData = res.data;
      })
    },
    setQuery(key, value) {
      this.queryItem[key] = value;
      this.getBackupUsers(false);
    },
    getPayHistoryList() {
      const params = this.payHistoryInfo.params;
      params.tenantId = params.tenantId || null
      getPayHistoryList(params).then(res=> {
        const data = res.data.records;
        if(!isEmpty(data)) {
          this.payHistoryInfo.list = data;
        }
      }).finally(()=> {});
    },
    getBackupUsers(nextPage) {
      if(nextPage) {
        this.pageInfo.params.current += 1;
      } else {
        if (!this.pageInfo.params.userId) {
          this.pageInfo.loadStatus = 'more';
          this.pageInfo.list = [];
          this.pageInfo.params.current = 1;
          this.pageInfo.params.userId = null;
          this.pageInfo.params.tenantId = null;
        }
        if (this.queryItem.tenantId) {
          this.pageInfo.params.tenantId = this.queryItem.tenantId;
        }
        console.log('getBackupUsers set query', this.pageInfo.params)
      }
      if(this.pageInfo.loadStatus !== 'more') {
        return;
      }
      uni.showLoading();
      getBackupUsers(this.pageInfo.params).then(res=> {
        const data = res.data.records;
        if(!isEmpty(data)) {
          if (this.pageInfo.params.userId) {
            for (let item of this.pageInfo.list) {
              if (item.userId === this.pageInfo.params.userId) {
                console.log('getBackupUsers', data[0], item)
                item.backup = data[0].backup;
              }
            }
          } else {
            this.pageInfo.list = this.pageInfo.list.concat(data);
          }
        }
        if(!data || data.length < this.pageInfo.size) {
          this.pageInfo.loadStatus = 'noMore'
        }
      }).finally(()=> {uni.hideLoading();});
    },
    afterChange(userId, tenantId) {
      this.pageInfo.params.userId = userId;
      this.pageInfo.params.tenantId = tenantId;
      this.getBackupUsers(false);
    },

    openPopup() {
      this.$refs.popup.open()
    },
    close() {
      this.$refs.popup.close()
    },
    boardClick(key, value) {
      this.changeData.key = key;
      this.changeData.value = value;
      this.openPopup();
    },
    confirm() {
      if (this.changeData.key === 'tenantId') {
        this.close()
        this.setQuery(this.changeData.key, this.changeData.value);
      } else if (this.changeData.key === 'backupTenantId') {
        changePayTenantId().then(res => {
          this.getDashboard();
          this.close()
        })
      } else {
        changeDashboardData(this.changeData).then(res => {
          this.getDashboard();
          this.close()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
page,
.content {
  height: 100%;
  background-color: $page-color-base;
  display: flex;
  flex-direction: column;
  .scroll-box {
    height: 100%;
    background-color: $page-color-base;
    padding-bottom: 20rpx;
  }
}
.board-table{
  display: flex;
  flex-wrap: wrap;
  margin-top: 24rpx;
  .board-item {
    width: 50%;
    align-items: center;
    justify-content: center;
    padding: 12rpx 24rpx;
  }
  .board-group {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 12rpx 24rpx;
    border: 2rpx solid #fff;
    .board-line {
      padding: 4rpx 0;
      .board-line-item {
        padding: 0 8rpx;
      }
      .color-red {
        color:red;
      }
      .color-green {
        color: #4cb84b;
      }
    }
  }
}
</style>