import { isEmpty } from '@/util/validate'
import { config } from '@/common/jz-constant';
export default {
	install(Vue) {
		Vue.mixin({
			onLoad() {
				this.params = this.getParams();
				console.log('public on onLoad', this.params)
			},
			data() {
				return {
					config: config,
					params: {},
				}
			},
			methods: {
				getParams() {
					let params = this.$Route.query;
					//console.log('getRouterObjParams', this.$Route)
					return params;
				},
				goBack() {
					this.$Router.back(1)
				},
				goto(path, params) {
					// if(!path.startsWith('/page')) {
					// 	path = '/pages' + path;
					// }
					this.$Router.push({ path: path, query: params})
				},
				goToTab(path) {
					this.$Router.replace(path)
				},
				gotoName(name, params) {
					console.log('gotoName', params)
					this.$Router.push({ name: name, params: params})
				},
				gotoIndex() {
					this.$Router.replace({ name: 'index'})
				},
				gotoRoute(to, replace) {
					const router = this.$Router;
					console.log('gotoRoute', to);
					// if(router.$lockStatus === true) {
					// 	router.$lockStatus = false;
					// }
					const route = { path: to.path, query: to.query };
					if(to.isTab) {
						router.pushTab({name: to.name});
					} else {
						if(replace) {
							router.replace(route);
						} else {
							router.push(route);
						}
					}
				},
				isEmpty(v) {
					return isEmpty(v);
				}
			}
		})
	}
}