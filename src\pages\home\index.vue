<template>
    <view class="content">
        <scroll-view class="list" scroll-y>
            <swiper class="swiper-top" indicator-dots="true" autoplay="true" interval="3000">
                <swiper-item v-for="(item, index) in items" :key="index">
                    <image :src="$website.ossDomain + item.mediaPath" mode="aspectFill" :lazy-load="true"></image>
                </swiper-item>
            </swiper>
            <view class="notice-box">
                <text class="ylh-icon icon-voice"></text>
                <swiper class="swiper-notice"  vertical="true" autoplay="false" duration="500" interval="4000">
                    <swiper-item v-for="(item, index) in notices" :key="index">
                        <text class="notice-text">{{item.textContent}}</text>
                    </swiper-item>
                </swiper>
            </view>
            <!--推介轮播-->
            <com-promo-series :groups="groups" :seriesHasGameMap="seriesHasGameMap"></com-promo-series>
            <uni-section title="今日推介" type="line"></uni-section>
            <view class="promo-list">
                <com-promo :item="item" v-for="(item, index) in promoList" :key="index" @buyGame="buy"></com-promo>
            </view>
            <charge ref="popup" title="余额不足，请及时充值"></charge>
        </scroll-view>
    </view>
</template>

<script>
import comPromoSeries from './components/com-promo-series'
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from './components/com-promo'
import {getSimpleAllList,getTodayGameList} from '@/common/request/promo-series'
import {getAdItemsByType} from '@/common/request/ad'
import promoBuy from './mixins/promo-buy'
import charge from "../customer/components/charge";

export default {
    components: {comPromoSeries,uniSection,uniCard,comPromo,charge},
    mixins: [promoBuy],
    data() {
        return {
            promoList: [],
            items: [],
            notices: [],
            groups: [],
            seriesHasGameMap: {},
        }
    },
    onLoad() {
        this.getSimpleAllList();
    },
    onShow() {
        this.getData();
    },
    methods: {
        getData() {
            getTodayGameList().then(res=> {
                this.promoList = res.data;
                this.seriesHasGameMap = {};
                for(let game of res.data) {
                    if(!game.result) {
                        const key = game.promoSeriesId + '';
                        let count = this.seriesHasGameMap[key] || 0;
                        count += 1;
                        this.seriesHasGameMap[key] = count;
                    }
                }
                getSimpleAllList().then(res=> {
                    this.groups = [];
                    for(let i=0; i < res.data.length; i+=6) {
                        if(i===0 || i%6===0) {
                            let items = res.data.slice(i,i+6);
                            this.groups.push({items: items});
                        }
                    }
                }).catch(res=>{});
            }).catch(res=>{});
        },
        getSimpleAllList() {
            this.groups = [];
            getAdItemsByType(0).then(res=> {
                this.items = res.data;
            }).catch(res=>{});
            getAdItemsByType(1).then(res=> {
                this.notices = res.data;
            }).catch(res=>{});
        },
    }
}
</script>

<style lang="scss">
.list {
    height: 100%;
}
.com-title {
    width: 100%;
    .ylh-icon{
        color: $primary-color-dark;
        font-size: $font-base -2rpx;
        margin-right: $page-row-spacing - 4rpx;
    }
}
.swiper-top {
    height: 358rpx !important;;
    image {
        width: 100%;
        height: 100%;
    }
}
.game-result-icon {

}
.notice-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 18rpx;
    .icon-voice {
        width: 44rpx;
    }
    .swiper-notice {
        flex: auto;
        height: 62rpx;
        line-height: 62rpx;
    }
    .notice-text {
        color: #dd524d;
    }
}
</style>
