import {get, getByService, postByService} from "@/common/request/jz-request";
import {config} from "@/common/jz-constant";

export const getMyWallet = () => get('/wallet/getMyWallet?v=' + config.version)
export const getMyWalletByUserId = (userId) => get('/wallet/getMyWalletByUserId', {userId: userId})
export const getBalanceHistory = params => get('/balance/history/list', params)
export const getMyWxAccountFansInfo = () => getByService('admin', '/wxaccountfans/getMyWxAccountFansInfo')
export const getWxAccountFansInfoByUserId = (userId, tenantId) => getByService('admin', '/wxaccountfans/getWxAccountFansInfoByUserId', {userId: userId, originTenantId: tenantId})
export const updatePhone = data => postByService('admin', '/wxaccountfans/phone/bind', data)
export const updatePassword = data => postByService('admin', '/wxaccountfans/updatePassword', data)
