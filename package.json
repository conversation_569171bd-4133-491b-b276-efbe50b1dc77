{"name": "ylh-wechat", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "local:a": "node src/env/manifest-online.js a 47 0 wechat && npm run dev:h5", "local:b": "node src/env/manifest-local.js b 47 0 wechat && npm run dev:h5", "local:c": "node src/env/manifest-local.js c 47 0 wechat && npm run dev:h5", "local:d": "node src/env/manifest-local.js d 47 0 wechat && npm run dev:h5", "local:e": "node src/env/manifest-local.js e 47 0 wechat && npm run dev:h5", "local:e:h5": "node src/env/manifest-local.js e 47 1 wechat && npm run dev:h5", "local:f": "node src/env/manifest-local.js f 47 0 wechat && npm run dev:h5", "local:g": "node src/env/manifest-local.js g 47 0 wechat && npm run dev:h5", "local:i": "node src/env/manifest-local.js i 92 0 wechat && npm run dev:h5", "local:j": "node src/env/manifest-local.js j 93 0 wechat && npm run dev:h5", "local:x": "node src/env/manifest-local.js x 1009 0 wechat && npm run dev:h5", "local:x1": "node src/env/manifest-local.js x1 1029 0 wechat && npm run dev:h5", "local": "node src/env/manifest-prod.js a 48 0 wechat && npm run dev:h5", "local:yzly": "node src/env/manifest-local.js yzly 47 0 wechat && npm run dev:h5", "prod:yzly": "node src/env/manifest-prod.js yzly 3 0 wechat && npm run build:h5", "prod:a": "node src/env/manifest-prod.js a 48 0 gEVaN gwechat && npm run build:h5", "prod:b": "node src/env/manifest-prod.js b 90 0 fwechat ewechat && npm run build:h5", "prod:c": "node src/env/manifest-prod.js c 87 0 cwechat bwechat && npm run build:h5", "prod:d": "node src/env/manifest-prod.js d 84 0 HpzbY dNdNo && npm run build:h5", "prod:e": "node src/env/manifest-prod.js e 65 0 bwechat awechat && npm run build:h5", "prod:f": "node src/env/manifest-prod.js f 81 0 hwechat gwechat && npm run build:h5", "prod:g": "node src/env/manifest-prod.js g 47 0 dwechat cwechat && npm run build:h5", "prod:i": "node src/env/manifest-prod.js i 92 0 awechat && npm run build:h5", "prod:j": "node src/env/manifest-prod.js j 93 0 wechat && npm run build:h5", "prod:x": "node src/env/manifest-prod.js x 1009 0 wechat && npm run build:h5", "prod:x1": "node src/env/manifest-prod.js x1 1029 0 wechat && npm run build:h5", "prod:a:h5": "node src/env/manifest-prod.js a 48 1 h5 && npm run build:h5", "prod:b:h5": "node src/env/manifest-prod.js b 90 1 h5 wechat && npm run build:h5", "prod:c:h5": "node src/env/manifest-prod.js c 84 1 h5 && npm run build:h5", "prod:d:h5": "node src/env/manifest-prod.js d 84 1 h5 && npm run build:h5", "prod:e:h5": "node src/env/manifest-prod.js e 65 1 h5 && npm run build:h5", "prod:f:h5": "node src/env/manifest-prod.js f 81 1 h5 && npm run build:h5", "prod:g:h5": "node src/env/manifest-prod.js g 47 1 h5 && npm run build:h5", "prod:i:h5": "node src/env/manifest-prod.js i 91 1 h5 && npm run build:h5", "prod:j:h5": "node src/env/manifest-prod.js j 93 1 h5 && npm run build:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build --minimize", "build:h5:dev": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build", "build:h5:dev:watch": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build --watch", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i"}, "dependencies": {"@dcloudio/uni-app-plus": "2.0.2-3061720230112004", "@dcloudio/uni-h5": "2.0.2-3061720230112004", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-i18n": "2.0.2-3061720230112004", "@dcloudio/uni-mp-360": "2.0.2-3061720230112004", "@dcloudio/uni-mp-alipay": "2.0.2-3061720230112004", "@dcloudio/uni-mp-baidu": "2.0.2-3061720230112004", "@dcloudio/uni-mp-jd": "2.0.2-3061720230112004", "@dcloudio/uni-mp-kuaishou": "2.0.2-3061720230112004", "@dcloudio/uni-mp-lark": "2.0.2-3061720230112004", "@dcloudio/uni-mp-qq": "2.0.2-3061720230112004", "@dcloudio/uni-mp-toutiao": "2.0.2-3061720230112004", "@dcloudio/uni-mp-vue": "2.0.2-3061720230112004", "@dcloudio/uni-mp-weixin": "2.0.2-3061720230112004", "@dcloudio/uni-mp-xhs": "2.0.2-3061720230112004", "@dcloudio/uni-quickapp-native": "2.0.2-3061720230112004", "@dcloudio/uni-quickapp-webview": "2.0.2-3061720230112004", "@dcloudio/uni-stacktracey": "2.0.2-3061720230112004", "@dcloudio/uni-stat": "2.0.2-3061720230112004", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "crypto-js": "^3.1.9-1", "flyio": "^0.6.2", "jweixin-module": "^1.6.0", "regenerator-runtime": "^0.12.1", "uni-read-pages": "^1.0.5", "uni-simple-router": "2.0.6", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@babel/runtime": "~7.17.9", "@dcloudio/types": "^3.0.4", "@dcloudio/uni-automator": "2.0.2-3061720230112004", "@dcloudio/uni-cli-i18n": "2.0.2-3061720230112004", "@dcloudio/uni-cli-shared": "2.0.2-3061720230112004", "@dcloudio/uni-migration": "2.0.2-3061720230112004", "@dcloudio/uni-template-compiler": "2.0.2-3061720230112004", "@dcloudio/vue-cli-plugin-hbuilderx": "2.0.2-3061720230112004", "@dcloudio/vue-cli-plugin-uni": "2.0.2-3061720230112004", "@dcloudio/vue-cli-plugin-uni-optimize": "2.0.2-3061720230112004", "@dcloudio/webpack-uni-mp-loader": "2.0.2-3061720230112004", "@dcloudio/webpack-uni-pages-loader": "2.0.2-3061720230112004", "@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-service": "~4.5.19", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "node-sass": "^4.14.1", "postcss-comment": "^2.0.0", "sass-loader": "^7.3.1", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}