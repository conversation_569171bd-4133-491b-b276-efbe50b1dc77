import groovy.json.JsonSlurperClassic
pipeline {
    agent any
    tools {
        nodejs 'nodejs'
    }
    stages {
        stage('下载') {
            steps {
                git branch: 'G', credentialsId: 'f298ab39-be73-4a2a-aa24-66c2852a3d87', url: 'http://127.0.0.1:59000/m2276699/football-ui.git'
            }
        }
        stage('打包发布') {
            steps {
                script {
                    def cc =  "test -d ${WORKSPACE}/node_modules && echo \"目录存在\" || cp -R /home/<USER>/workspace/football-H5-ui-A/node_modules ${WORKSPACE}"
                    println cc
                    sh cc
                    println "【开始打包】"
                    // sh "npm -v"
                    // println "${WORKSPACE}"
                    println ChangeNewPath
                    Products.tokenize(',').each{product ->
                        def p = product.toLowerCase();
                        sh "rm -rf ${WORKSPACE}/dist"
                        println "【已删除dist】"

                        BuildMap = [:];
                        def iniStr = readFile('build.properties')
                        println iniStr
                        iniStr.tokenize('\n').each{envStr ->
                            def env = envStr.split('=');
                            def envKey = env[0].trim()
                            def envValue = env[1].trim()
                            BuildMap[envKey] = envValue;
                        }

                        def APP_ID = BuildMap['VUE_APP_ID'];
                        def NEW_PATH = BuildMap['WEBSITE_PATH'];
                        def OLD_PATH = BuildMap['LAST_WEBSITE_PATH'];
                        def isChangeGit = false;

                        def lastPath = 'src/env/params/' + p + '.path'
                        def fileExists = fileExists lastPath
                        if (fileExists) {
                            // 存在则赋值到当前目录
                            OLD_PATH = readFile lastPath
                            println OLD_PATH
                        } else {
                            writeFile file: lastPath, text: NEW_PATH
                            def addGitCmd = "git add " + lastPath
                            println addGitCmd
                            println 'NEW_PATH=' + NEW_PATH
                            withCredentials([gitUsernamePassword(credentialsId: 'f298ab39-be73-4a2a-aa24-66c2852a3d87', gitToolName: 'Default')]){
                                sh addGitCmd
                            }
                            isChangeGit = true
                        }
                        // def shContent = readFile(file: 'src/env/sh/' + p + '.sh')

                        if (ChangeNewPath == 'true') {
                            NEW_PATH = NanoIdGenerator.generateNanoId(5)
                            println "Generated Nano ID: ${NEW_PATH}"
                            writeFile file: lastPath, text: NEW_PATH
                            isChangeGit = true
                        }

                        if (isChangeGit) {
                            def commitGitCmd = 'git commit -a -m "By Jenkins"'
                            def pushGitCmd = 'git push origin G'
                            println commitGitCmd
                            println pushGitCmd
                            withCredentials([gitUsernamePassword(credentialsId: 'f298ab39-be73-4a2a-aa24-66c2852a3d87', gitToolName: 'Default')]){
                                sh commitGitCmd
                                sh pushGitCmd
                            }
                        }


                        // 动态构建打包命令，传递NEW_PATH和OLD_PATH参数
                        def buildCmd = "node src/env/manifest-prod.js ${p} ${APP_ID} 0 ${NEW_PATH} ${OLD_PATH} && npm run build:h5"

                        println "buildCmd: ${buildCmd}"
                        println "NEW_PATH: ${NEW_PATH}"
                        println "OLD_PATH: ${OLD_PATH}"
                        println "APP_ID: ${APP_ID}"

                        sh buildCmd

                        def remotePath = product.toUpperCase() + '/' + NEW_PATH + '/' + APP_ID + '/';

                        println 'NewPath=' + NEW_PATH
                        println 'remotePath=' + remotePath

                        // sshPublisher(publishers: [sshPublisherDesc(configName: '********', transfers: [sshTransfer(cleanRemote: true, excludes: '', execCommand: '', execTimeout: 120000, flatten: false, makeEmptyDirs: true, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: remotePath, remoteDirectorySDF: false, removePrefix: 'dist/build/h5/', sourceFiles: 'dist/build/h5/**')], usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
                        echo '【已发布' + product + '】'

                        if (OLD_PATH != '' && OLD_PATH != 'undefined' && OLD_PATH != NEW_PATH) {
                        	def cmd1 = 'find /etc/nginx/conf.d/ -name "' + product.toUpperCase() + '.conf" -print | xargs perl -pi -e ' + "'s/" + OLD_PATH + "/" + NEW_PATH + "/g'"
                            def cmd2 = 'rm -rf /home/<USER>/' + product.toUpperCase() + '/' + OLD_PATH
                            def cmd3 = 'systemctl reload nginx'
                            println cmd1
                            println cmd2
                            println cmd3
                            withCredentials([usernamePassword(credentialsId: '********', passwordVariable: 'password', usernameVariable: 'username')]) {
                    			script {

                    				def remote = [:]
                    				remote.name = '********'
                    				remote.host = '********'
                    				remote.user = "$username"
                    				remote.password = "$password"
                    				remote.allowAnyHosts = true

                    				//sshCommand remote: remote, command: cmd1
                    				//sshCommand remote: remote, command: cmd2
                    				//sshCommand remote: remote, command: cmd3
                    			}
                            }
                            // sh cmd1
                            // sh cmd2
                            // sh cmd3
                        }

                        // sh "mv ${WORKSPACE}/dist ${WORKSPACE}/dist" + product
                    }
                    // sleep time: 10
                }
            }
        }
    }
}

class NanoIdGenerator {
    private static final java.security.SecureRandom random = new java.security.SecureRandom()
    private static final String ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'

    static String generateNanoId(int length = 21) {
        StringBuilder sb = new StringBuilder(length)
        for (int i = 0; i< length; i++) {
            int index = random.nextInt(ALPHABET.length())
            sb.append(ALPHABET.charAt(index))
        }
        return sb.toString()
    }
}
