<template>
    <swiper class="swiper" :indicator-dots="true" :autoplay="false" :interval="10000" :duration="500">
        <swiper-item v-for="(group, index) in groups" :key="index">
            <uni-grid :column="3" :showBorder="false">
                <uni-grid-item v-for="(item, index) in group.items" :key="index">
                    <view class="series-item" @click="goto('/series', {promoSeriesId: item.id})">
                        <view class="series-img">
                            <image :src="item.image|productImg" :lazy-load="true"/>
                            <uni-badge v-if="getCount(item)" class="order-cnt" :text="getCount(item)" type="error" />
                        </view>
                        <view class="series-title">{{item.name}}</view>
                    </view>
                </uni-grid-item>
            </uni-grid>
        </swiper-item>
    </swiper>

</template>

<script>
import UniGrid from '@/components/uni-grid/uni-grid'
import UniGridItem from '@/components/uni-grid/uni-grid-item'
import UniBadge from '@/components/uni-badge';

export default {
    name: "comPromoSeries",
    components: {UniGrid,UniGridItem,UniBadge},
    props: {
        // 每列显示个数
        groups: {
            type: Array,
            default: function(){
                return [];
            }
        },
        seriesHasGameMap: {
            type: Object,
            default: function(){
                return {};
            }
        }
    },
    methods: {
        getCount(item) {
            return this.seriesHasGameMap[item.id+''];
        }
    }
}
</script>

<style lang="scss" scoped>
.swiper {
    padding: 10rpx 40rpx 0 40rpx;
    height: 420rpx;
    background-color: #fff;
}
.series-title {
    text-align: right;
}
.series-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    .series-img {
        display: flex;
        justify-content: center;
        width: 100%;
        image {
            border-radius:50%;
            width: 100rpx;
            height: 100rpx;
        }
    }
    .series-title {
        text-align: center;
        padding: 8rpx;
    }
}
.order-cnt {
    position: absolute;
    top: 10upx;
    right: 30upx;
}
</style>