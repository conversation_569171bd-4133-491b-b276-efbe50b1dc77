
//提示框
import {isEmpty} from "@/util/validate";

export const showMsg = (title, duration = 3000, mask = false, icon = 'none') => {
	//统一提示方便全局修改
	if (<PERSON><PERSON><PERSON>(title) === false) {
		return;
	}
	uni.showToast({
		title,
		duration,
		mask,
		icon
	});
}

//提示框-confirm
export const showMsgConfirmSingle = (content, confirmText, successFun) => {
	//统一提示方便全局修改
	if (Boolean(content) === false) {
		return;
	}
	uni.showModal({
		title: '温馨提示',
		content: content,
		confirmText: confirmText,
		showCancel: false,
		success: function(res) {
			if (res.confirm) {
				if (successFun) {
					successFun();
				}
			}
		},
		fail: function(res) {

		}
	});
}

export const showMsgSingle = (content, confirmText, successFun) => {
	uni.showModal({
		title: '温馨提示',
		content: content,
		confirmText: confirmText,
		showCancel: false,
		success: function(res) {
			if (res.confirm) {
				if (successFun) {
					successFun();
				}
			}
		},
		fail: function(res) {

		}
	});
}

//提示框-confirm
export const showMsgConfirm = (title, content, cancelText, confirmText, cancelFun, confirmFun) => {
	//统一提示方便全局修改
	if (Boolean(content) === false) {
		return;
	}
	uni.showModal({
		title: title || '温馨提示',
		content: content,
		cancelText: cancelText,
		confirmText: confirmText,
		success: function(res) {
			if (res.confirm) {
				if (confirmFun) {
					confirmFun();
				}
			} else if (res.cancel) {
				if (cancelFun) {
					cancelFun();
				}
			}
		},
		fail: function(res) {

		}
	});
}

/**
 * 生成唯一的ID
 */
export const guid = () => {
	let counter = 0;
	let guid = (+new Date()).toString( 32 ),
		i = 0;
	for ( ; i < 6; i++ ) {
		guid += Math.floor( Math.random() * 65535 ).toString( 32 );
	}
	return guid + (counter++).toString( 32 );
};


export const stringify = (paramsObject) => {
	if(paramsObject && Object.keys(paramsObject).length > 0) {
		let newObj = {};
		for(let key in paramsObject) {
			let val = paramsObject[key];
			if(val) {
				newObj[key] = val;
			}
		}
		if(Object.keys(newObj).length > 0) {
			return JSON.stringify(newObj);
		}
	}
	return null;
};



export function parseTime(time, cFormat) {
	if (arguments.length === 0 || !time) {
		return null
	}

	let date
	if (typeof time === 'object') {
		date = time
	} else {
		if ((typeof time === 'string')) {
			if ((/^[0-9]+$/.test(time))) {
				time = parseInt(time)
			} else {
				time = time.replace(new RegExp(/-/gm), '/')
			}
		}
		if ((typeof time === 'number') && (time.toString().length === 10)) {
			time = time * 1000
		}
		date = new Date(time)
	}

	const o = {
		'M+': date.getMonth() + 1, // 月份
		'd+': date.getDate(), // 日
		'h+': date.getHours(), // 小时
		'm+': date.getMinutes(), // 分
		's+': date.getSeconds(), // 秒
		'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
		'S': date.getMilliseconds() // 毫秒
	}
	if (/(y+)/.test(cFormat)) { cFormat = cFormat.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length)) }
	for (const k in o) {
		if (new RegExp('(' + k + ')').test(cFormat)) { cFormat = cFormat.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length))) }
	}
	return cFormat
}

/**
 * 检查登录状态及执行目标方法
 * 应用于从第三方小程序进入时，路由可能没拦截的问题
 * @param view 页面，sample:this
 * @param success 已登录时执行的方法，sample:this.init
 */
export const checkLogin = (view, success) => {
	if (!view.$isLogin()) {
		console.log('checkLogin 没有登录，开始跳转授权页')
		view.$gotoAuth(view.$Router.$Route);
	} else {
		console.log('checkLogin 已登录，执行方法')
		success();
	}
}

export const bindPageInfo = (pkg, data) => {
	if(!isEmpty(data)) {
		pkg.list = pkg.list.concat(data);
	}
	//console.log('loadStatus', data.length)
	if(!data || data.length < pkg.params.size) {
		pkg.loadStatus = 'noMore'
	}
	return pkg;
}

export const bindScrollPageInfo = (pageInfo, nextPage) => {
	return new Promise((resolve, reject) => {
		if(nextPage) {
			pageInfo.params.current += 1;
		} else {
			pageInfo.loadStatus = 'more';
			pageInfo.list = [];
			pageInfo.params.current = 1;
		}
		if(pageInfo.loadStatus !== 'more') {
			reject()
		} else {
			resolve(pageInfo);
		}
	});

}
