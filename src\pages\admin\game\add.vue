<template>
    <view class="content">
        <view class="form-box">
            <uni-forms :modelValue="formData">
                <uni-forms-item required label="比赛名称" name="gameName">
                    <uni-easyinput type="text" v-model="formData.gameName" placeholder="请输入比赛名称" />
                </uni-forms-item>
                <uni-forms-item required name="promoSeriesId" label="赛区">
                    <uni-data-picker placeholder="请选择赛区" popup-title="请选择赛区" v-model="formData.promoSeriesId" :localdata="promoSeriesOptions"
                                     :step-searh="false">
                    </uni-data-picker>
                </uni-forms-item>
                <uni-forms-item required name="playTime" label="比赛时间">
                    <uni-datetime-picker type="datetime" v-model="formData.playTime" />
                </uni-forms-item>
                <uni-forms-item required label="主队" name="home">
                    <uni-easyinput type="text" v-model="formData.home" placeholder="请输入主队" />
                </uni-forms-item>
                <uni-forms-item required label="客队" name="away">
                    <uni-easyinput type="text" v-model="formData.away" placeholder="请输入客队" />
                </uni-forms-item>
                <uni-forms-item required name="result" label="预测结果">
                    <uni-data-picker placeholder="请选择预测结果" popup-title="请选择预测结果" v-model="formData.result"
                                     :localdata="resultOptions"
                                     :step-searh="false">
                    </uni-data-picker>
                </uni-forms-item>
                <uni-forms-item required name="promotion" label="推介内容">
                    <uni-easyinput type="textarea" v-model="formData.promotion" placeholder="请输入推介内容" />
                </uni-forms-item>
            </uni-forms>
            <button @click="submitForm">提交</button>
        </view>
    </view>
</template>

<script>
import uniForms from '@/components/uni-forms/uni-forms'
import uniFormsItem from '@/components/uni-forms-item/uni-forms-item'
import uniEasyinput from '@/components/uni-easyinput/uni-easyinput'
import uniDataPicker from '@/components/uni-data-picker/uni-data-picker'
import uniDatetimePicker from '@/components/uni-datetime-picker/uni-datetime-picker'
import {addGamePromo} from "@/common/request/admin";

export default {
    name: "promoAdd",
    components: {uniForms, uniFormsItem, uniEasyinput, uniDataPicker, uniDatetimePicker},
    computed: {
        ...mapState({
            userInfo: state => {return state.user.userInfo},
        })
    },
    data() {
        return {
            hasRole: false,
            formData: {
                id: '',
                promoSeriesId: ''
            },
            promoSeriesOptions: [
                // {value: 'bj',text: '北京'},
                // {value: 'sh',text: '上海'},
                // {value: 'gz',text: '广州'}
            ],
            resultOptions: [],
        }
    },
    onload() {
        const authorities = this.userInfo.authorities;
        if(authorities) {
            for(let x of authorities) {
                if(x.authority === 'football_gamepromo_add') {
                    this.hasRole = true;
                }
            }
        }
        if(this.hasRole === true) {
            const options = this.getParams();
            this.formData.id = options.id;
            this.getData();
        }
    },
    methods: {
        getData() {

        },
        getPromoSeriesPickerData() {

        },
        getResultPickerData() {

        },
        onchange() {

        },
        submitForm() {
            addGamePromo(this.formData).then(res=> {

            }).catch(()=>{});
        }
    }
}
</script>

<style lang="scss">
.form-box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 40rpx 20rpx 20rpx 20rpx;
}
</style>