<template>
  <view class="content">
    <slot/>
    <jz-mask :visible="showMask" :showIcon="true"></jz-mask>
  </view>
</template>

<script>

export default {
  name: 'wechatMp',
  props: {
    showMask: false
  },
  data() {
    return {
    }
  },
  method: {

  }
}
</script>
<style lang="scss">
uni-page-body,html,body{
    height: 100%;
}
page{ height: 100%; }
//page {
//    height: calc(100vh - 100rpx);
//}
//uni-page-body {
//    height: 100%;
//}
.content {
    height: 100%;
    padding-bottom: 16rpx;
    background-color: $page-color-base;
}
</style>