<template>
  <view class="com-card-box" @click="goto('/game', {id: item.lastGamePromo.id ,promoSeriesId: item.id})">
    <view class="com-game-top">
      <view class="com-game-idx">
        <view class="jz-button">{{ index }}</view>
      </view>
      <view class="com-game-tile">
        <text>
          {{ item.name }}
        </text>
        <view v-if="item.followId!==undefined" class="com-game-follow full-shadow">
          <view v-if="!item.followId" class="jz-button full-border" @click.stop="followGame(item.id)">
            <text class="ylh-icon icon-jia"></text>
            <text class="jz-button-text">关注</text>
          </view>
          <view v-else class="jz-button full-border" @click.stop="unFollowGame(item.id)">
            <text class="ylh-icon icon-gouxuan"></text>
            <text class="jz-button-text">已关注</text>
          </view>
        </view>
      </view>
    </view>
    <view class="com-game-bottom">
      <view class="com-game-image">
        <image :src="$website.ossDomain + item.image" mode="aspectFill" :lazy-load="true"></image>
      </view>
      <view class="com-game-info-box">

        <view class="com-game-tag-box">
          <text class="game-tag m-r" :class="{'blink y-chul':item.hasPromotion, 'w-chul': !item.hasPromotion}">
            {{ item.hasPromotion === true ? '已出料' : '未出料' }}
          </text>
          <!--<text class="game-tag m-r" v-if="!canBuy(item)">￥{{item.priceDay}}</text>-->
          <!--<text class="game-tag m-r">周胜率 100%</text>-->
          <!--<text class="game-tag m-r" v-if="winTimes">{{winTimes}}</text>-->
          <text class="game-tag m-r" v-if="rateItem.rate">{{ rateItem.rateText }}:{{ rateItem.rate }}%</text>
          <text class="x-month" v-if="item.lastGamePromo && includeMonth">{{ includeMonth }}</text>
          <text class="x-month-test" v-if="item.lastGamePromo && includeMonth">收录{{ includeMonth }}月</text>
        </view>
        <view class="com-game-play-time">比赛时间：{{ item.lastGamePromo.playTime | date('MM-dd hh:mm') }}</view>
        <view class="com-game-away-home">对阵：{{ item.lastGamePromo.home }} VS
          {{ item.lastGamePromo.away }}（{{ item.lastGamePromo.gameName }}）
        </view>
        <view class="com-game-buy">
          <view class="jz-button" :class="{'bg-view':!canBuy(item)}"
                @click.stop="goto('/game', {id: item.lastGamePromo.id ,promoSeriesId: item.id})">
            <!--<text class="ylh-icon icon-coin"></text>-->
            <text class="jz-button-text">点击进行订阅</text>
          </view>
        </view>
        <view class="com-game-price">
          <view class="game-price">
            <text class="font-bold font-red ylh-icon icon-taojinbi">{{ item.priceDay }}</text>
            订阅
          </view>
          <view class="game-result">
            <com-promo-win-times :results="results"></com-promo-win-times>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import UniGrid from '@/components/uni-grid/uni-grid'
import UniGridItem from '@/components/uni-grid/uni-grid-item'
import UniCard from '@/components/uni-card'
import uniLoadMore from '@/components/uni-load-more'
import jzEmpty from '@/components/jz-empty';
import {buyGame} from '@/common/request/order'
import {isEmpty} from "@/util/validate";
import {followGame, unFollowGame, getGameResultText} from '@/common/request/promo-series'
import {showMsgConfirm} from "@/common/jz-util"
import ComPromoWinTimes from "./com-promo-win-times";
// import UniFav from '@/components/uni-fav'

export default {
  name: "comPromoSeries",
  components: {ComPromoWinTimes, UniGrid, UniGridItem, UniCard, uniLoadMore, jzEmpty},
  props: {
    index: {
      type: Number
    },
    item: {
      type: Object,
      default: function () {
        return {};
      }
    },
    weekRate: {
      type: Boolean,
      default: function () {
        return true;
      }
    }
  },
  filters: {},
  data() {
    return {
      results: [],
      winTimes: '',
      includeMonth: 0,
      rateItem: {
        rate: undefined,
        rateText: undefined
      }
    }
  },
  mounted() {
    if (this.item.id) {
      this.getResults();
      this.setRateItem();
      this.includeMonth = this.monthsBetween(this.item.includeDate);
    }
  },
  methods: {
    canBuy(item) {
      const gamePromo = item.lastGamePromo;

      //没有赛事不能买
      if (gamePromo == null) {
        return false;
      }
      //未购买过
      const isNotBuy = isEmpty(item.buyTime);

      // if(item.type === '篮球' && isNotBuy) {
      //     return true;
      // }

      //未到开始时间
      let gameNotStart = false;
      if (gamePromo.playTime) {
        let now = new Date();
        //console.log('canBuy', gamePromo.playTime)
        let playTime = new Date(gamePromo.playTime.replace(/-/g, "/"));
        playTime = new Date(playTime.setHours(playTime.getHours() + 2))
        gameNotStart = playTime > now;
      }

      //已出料 && 未到开始时间 && 未发布结果 && 未购买过
      return item.hasPromotion && gameNotStart && isEmpty(gamePromo.result) && isNotBuy
    },
    buy(game) {
      if (this.canBuy(game)) {
        this.$emit('buyGame', game);
      }
    },
    getCombineGames(games) {
      const gameArray = games.split('\n');
      let combineGamesList = [];
      for (let game of gameArray) {
        const gameContentArray = game.split(",");
        if (gameContentArray.length === 3) {
          combineGamesList.push({playTime: gameContentArray[0], home: gameContentArray[1], away: gameContentArray[2]});
        }
      }
      return combineGamesList;
    },
    getResults() {
      const me = this;
      getGameResultText(me.item.id).then(res => {
        if (res.data) {
          me.results = res.data.split('');
          let times = 0;
          let searchFlag = true;
          for (let i = 0; i < this.results.length; i++) {
            if (searchFlag) {
              if (this.results[i] === 'W') {
                times++;
              } else {
                searchFlag = false;
              }
            }

          }
          if (times > 1) {
            me.winTimes = times + '连胜';
          }
        }
      }).catch(res => {
      });
    },
    setRateItem() {
      console.log('setRateItem')
      const promoSeries = this.item;
      if (this.weekRate) {
        if (promoSeries.weekRate != null && promoSeries.weekRate >= 0) {
          this.rateItem.rate = promoSeries.weekRate;
          this.rateItem.rateText = '周胜率';
        }
      } else {
        if (promoSeries.monthRate != null && promoSeries.monthRate >= 0) {
          this.rateItem.rate = promoSeries.monthRate;
          this.rateItem.rateText = '月胜率';
        }
      }
    },
    monthsBetween(date1) {
      //console.log('monthsBetween', date1)
      if (!date1) {
        return 0;
      }
      date1 = date1.split("-");
      const date2 = new Date();
      const year2 = date2.getFullYear();
      const month2 = date2.getMonth() + 1;
      const year1 = parseInt(date1[0]), month1 = parseInt(date1[1]);
      return (year2 - year1) * 12 + (month2 - month1) + 1;
    },
    followGame(promoSeriesId) {
      const me = this;
      followGame({promoSeriesId: promoSeriesId}).then(res => {
        me.item.followId = '1';
      }).catch(res => {
      });
    },
    unFollowGame(promoSeriesId) {
      const me = this;
      unFollowGame(promoSeriesId).then(res => {
        me.item.followId = 0;
      }).catch(res => {
      });
    },
  }
}
</script>

<style lang="scss">
.uni-card {
  margin-left: 0;
  margin-right: 0;
}

.com-card-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 8rpx;
  border: 1px solid $border-color-base;

  .com-game-top {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 80rpx;
    padding: 0 12rpx;

    .com-game-tile {
      color: $primary-color-game-title;
      font-size: $font-lg;
      font-weight: 700;
      padding: 4rpx 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .com-game-follow {
        .jz-button {
          font-size: $font-sm;
          background-color: transparent;
          color: $primary-color-light;
          padding: 6rpx 8rpx;

          .ylh-icon {
            font-size: $font-base;
          }

          .jz-button-text {
            padding-left: 4rpx;
          }
        }
      }
    }
  }

  .com-game-bottom {
    display: flex;
    flex-direction: row;
    flex: auto;
    width: 100%;
    padding: 0 12rpx;
  }

  .com-game-idx {
    width: 60rpx;
    text-align: center;
    padding: 16rpx 10rpx 16rpx 0;

    .jz-button {
      padding: 4rpx;
    }
  }

  .com-game-image {
    width: 160rpx;
    height: 160rpx;

    image {
      //border-radius:50%;
      width: 100%;
      height: 100%;
    }
  }

  .com-game-info-box {
    flex: auto;
    padding-left: 16rpx;

    .com-game-play-time {
      font-size: $font-sm + 2rpx;
      color: $font-color-disabled;
      padding: 4rpx 0;
    }

    .com-game-away-home {
      font-size: $font-sm + 2rpx;
      color: $font-color-disabled;
      padding: 4rpx 0;
    }

    .com-game-buy {
      padding: 6rpx 12rpx 6rpx 0;
    }

    .com-game-price {
      display: flex;
      flex-direction: row;
      font-size: $font-sm;
      justify-content: space-between;
      padding: 4rpx 0 8rpx 0;

      .game-price {
        .ylh-icon {
          font-size: $font-sm;
        }
      }
    }
  }
}

.fav {
  margin-left: 20rpx;
}

.jz-button {
  font-size: $font-lg - 4rpx;
  padding: 12rpx 12rpx;
  background-color: $primary-color-button-buy;
}

.disabled {
  background-color: $font-color-disabled;
}

.font-red {
  color: red;
}

.font-bold {
  font-weight: 700;
}

/*.main{
      color: #666;margin-top: 50px;
    }*/
/* 定义keyframe动画，命名为blink */
@keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

/* 添加兼容性前缀 */
@-webkit-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

@-moz-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

@-ms-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

@-o-keyframes blink {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 0.2;
  }
}

/* 定义blink类*/
.blink {
  color: #F8F8FF;
  font-weight: bold;
  animation: blink 1s linear infinite;
  /* 其它浏览器兼容性前缀 */
  -webkit-animation: blink 1s linear infinite;
  -moz-animation: blink 1s linear infinite;
  -ms-animation: blink 1s linear infinite;
  -o-animation: blink 1s linear infinite;
}

.com-game-tag-box {
  padding: 4rpx 0;
  font-size: $font-base;

  .game-tag {
    display: inline-block;
    align-items: center;
    justify-content: center;
    background: $primary-color-button-buy;
    color: #ffffff;
    padding: 0 8rpx;
  }

  .y-chul {
    background: red;
  }

  .w-chul {
    background-color: #cccccc;
  }

  .m-r {
    margin-right: 8rpx;
  }

  .x-month {
    display: inline-block;
    color: #ffffff;
    background: #ffa258;
    padding: 0 4rpx;
  }

  .x-month-test {
    display: inline-block;
    color: #ffa258;
    background: #faf5d7;
    padding: 0 8rpx;
  }
}

.w-chul {
  display: inline-block;
  background-color: #cccccc;
  font-size: $font-base;
  padding: 0 10px;
}
</style>
