import {showMsgConfirm} from "@/common/jz-util";
import {buyGame,buyGameRaw} from "@/common/request/order";
import {isEmpty} from "@/util/validate";
import {number} from "@/common/jz-filter";

export default {
    name: 'promoBuy',
    data() {
        return {
            rawPay: false,
            activity: {},
            productList: [],
        };
    },
    methods: {
        openPopup() {
            this.$refs.popup.openPopup()
        },
        buy(game, orderType, channelType, onPaySuccess) {
            const me = this;
            const params = {
                productId: game.id,
                qty: 1,
                orderType: orderType || '单次',
                channelType: channelType || 'WX_JSAPI'
            }
            let price = game.priceDay;
            let memo = ' 查看该赛事？';
            switch (orderType) {
                case "单次":
                    price = game.priceDay;
                    break;
                case "包周":
                    price = game.priceWeek;
                    memo = " 包周？";
                    break;
                case "包月":
                    price = game.priceMonth;
                    memo = " 包月？";
                    break;
            }
            this.$store.dispatch('user/getWallet').then((wallet)=> {
                let msg = '是否';
                const allCoin = wallet.coin + wallet.backupCoin
                if(allCoin >= price) {
                    msg += '使用' + price + '金币';
                } else {
                    msg += '使用 ';
                    const wechatPay = number(price - allCoin);
                    const coinPay = allCoin;
                    if(coinPay > 0) {
                        msg += coinPay + ' 金币 + ';
                    }
                    msg += '微信支付 ' + wechatPay;
                    memo += "如取消支付，金币将在【30分钟】后自动退还。"
                }
                showMsgConfirm(
                    '订阅提示',
                    msg + memo,
                    '再想想',
                    '确认',
                    function() {
                    },
                    function() {
                        if(!me.rawPay) {
                            me.buyGame(params, game, onPaySuccess);
                        }
                        else {
                            me.buyGameRaw(params, game, onPaySuccess);
                        }
                    }
                );
            }).catch(res=>{
                console.log('获取钱包失败');
            });

        },
        buyGame(params, game, onPaySuccess) {
            const me = this;
            uni.showLoading();
            buyGame(params).then((res)=> {
                let data = res.data;
                data.productId = game.id;
                data.promoSeriesId = game.promoSeriesId
                uni.hideLoading();
                if(data.needPay) {
                    //需要支付
                    //直接支付
                    //me.$Router.push({ name: 'pay', query: data});
                    //弹框支付
                    me.openPopup();
                } else {
                    console.log('不需要支付', {id: game.id, promoSeriesId: data.promoSeriesId})
                    //不需要支付
                    // me.goto('/game', {id: game.id, promoSeriesId: data.promoSeriesId});
                    me.gotoRoute({path: "/game", query: {id: game.id, promoSeriesId: data.promoSeriesId}}, true);
                }
            }).catch(res=>{uni.hideLoading()});
        },
        buyGameRaw(params, game, onPaySuccess) {
            const me = this;
            uni.showLoading();
            buyGameRaw(params).then((res)=> {
                let data = res.data;
                data.productId = game.id;
                data.promoSeriesId = game.promoSeriesId
                uni.hideLoading();
                if(!isEmpty(data.wxPayMpOrderResult)) {
                    //需要支付
                    //直接支付
                    me.gotoName('pay', data);
                } else {
                    console.log('不需要支付', {id: game.id, promoSeriesId: data.promoSeriesId})
                    //不需要支付
                    // me.goto('/game', {id: game.id, promoSeriesId: data.promoSeriesId});
                    if (onPaySuccess) {
                        onPaySuccess(game)
                    } else {
                        me.gotoRoute({path: "/game", query: {id: game.id, promoSeriesId: data.promoSeriesId}}, true);
                    }
                }
            }).catch(res=>{uni.hideLoading()});
        }
    }
}
