<template>
	<view class="mask" :class="{'show':showFlag}">
		<view class="modal-box full-radius" :style="{top:settings.top}">
			<view class="modal-content">
				<view class="modal-title">
					{{settings.title}}
				</view>
				<view class="modal-form">
					<slot name="formTop"></slot>
					<view class="modal-form-item" :style="item.style" v-for="(item, index) in inputs" :key="index">
						<text v-if="item.icon" class="ylh-icon" :class="item.icon"></text>
						<checkbox-group v-if="item.checkboxItems" @change="checkboxChange($event,item.vModel)">
							<label class="uni-list-cell uni-list-cell-pd" v-for="(checkbox, index1) in item.checkboxItems" :key="index1">
								<view class="modal-form-item-checkbox">
									<checkbox :value="index1" :checked="checkbox.checked" v-bind:disabled="settings.buttonDisabled"></checkbox>
								</view>
								<view class="modal-form-item-checkbox-label">{{checkbox.name}}</view>
							</label>
						</checkbox-group>
                        <view class="uni-list" v-else-if="item.radioGroup">
                            <radio-group @change="radioChange($event, item)">
                                <label class="uni-list-cell" v-for="(radio, idxRadio) in item.radioGroup" :key="radio.value">
                                    <view>
                                        <radio :value="radio.value" :checked="values[item.vModel] === radio.value" />
                                    </view>
                                    <view class="radio-text">{{radio.text}}</view>
                                </label>
                            </radio-group>
                        </view>

						<input v-else class="modal-form-input" v-model="data[item.vModel]" :maxlength="item['maxlength']" :placeholder="item['placeholder']" />
						<!--<image class="modal-v-code" mode="aspectFit" v-if="item.vModel==='vCode'" :src="vCodeSrc" @click="changeVCode"></image>-->
						<button v-if="item.vModel==='vCode'" class="v-code" :class="{'v-code-exp':timerExpCount===60}" size="mini" @click="getPhoneCode(item['vModelPhone'])">
							<text>{{timerExpCount===60?'获取验证码':timerExpCount}}</text>
						</button>
					</view>
					<slot name="formButton"></slot>
				</view>
				
				<view class="modal-bottom">
					<!-- 按钮 -->
					<button v-if="!settings.buttonSimple" type="primary" class="action-btn" hover-class="item-hover" v-bind:disabled="settings.buttonDisabled" :hover-stay-time="50" @click="submit">{{settings.buttonText}}</button>
					<view v-if="settings.buttonSimple" class="simple-button" @click="submit">
						{{settings.buttonText}}
					</view>
				</view>
			</view>
	
			<!--关闭-->
			<view class="cancel-box" @click.stop="close()">
				<text class="ylh-icon icon-cuo"></text>
			</view>
		</view>
	</view>
</template>
	
<script>
//import uniDataChecklist from '@/components/uni-data-checkbox'
export default {
	/* 样例
	<jz-confirm-modal v-if="confirmObj.settings.display" :settings="confirmObj.settings" :inputs="confirmObj.inputs" :values="confirmObj.values" @submit="confirmChangeShop" @close="confirmObj.settings.display=false"></jz-confirm-modal>
	//配置
	confirmObj: {
		settings: {
			display: false,		//是否显示
			title: "修改门店",	//标题
			buttonText: '提交'	//确认按钮文本
		},
		inputs: [{
			vModel: 'shopNumber',		//双向绑定values.shopNumber字段
			icon: 'icon-mendian',		//input左侧icon
			maxlength: 10,				//最大长度
			placeholder: '请输入门店编号'	//placeholder
		}],
		values: {
			shopNumber: ''
		}
	}*/
    //components: [uniDataChecklist],
	props: {
		settings: {
			type: Object,
			default: function() {
			    return {
                    display: false,
                    title: '',
                    buttonSimple: false,
                    buttonText: '',
                    buttonDisabled: false
                }
			}
		},
		inputs: {
			type: Array,
            default: function() {
                return [];
            }
		},
		slotTemplate: {
			
		},
		values: {
			type: Object,
            default: function(){
                return {};
            }
		}
	},
	data() {
		return {
			data: {},
			vCode: '',
			timer: null,
			timerExpCount: 60,
			showFlag: false
		}
	},
	destroyed() {
		if(this.timer) {
			clearInterval(this.timer);
			this.timer = null;
		}
		this.timerExpCount = 60;
	},
	mounted() {
		this.data = this.values;
		this.showFlag = this.settings.display;
	},
	methods: {
		submit(){
			this.$emit("submit", this.data);
		},
		close(){
			this.$emit("close");
		},
		// changeVCode(){
		// 	this.vCodeSrc = settings.restUrl + 'customer/public/captcha?timestamp=' + (new Date()).valueOf()
		// },
		getPhoneCode(phoneField){
			if(this.timerExpCount!==60) {
				return;
			}
			if (!this.timer) {
				this.timer = setInterval(() => {
					if (this.timerExpCount > 1) {
						this.timerExpCount--;
					} else {
						clearInterval(this.timer);
						this.timer = null;
						this.timerExpCount = 60;
					}
				}, 1000)
			}
			let phone = this.data[phoneField];
			if(!this.$jzUtil.isEmpty(phone)) {
				this.$jzApi.auth.sendSmsCode({phoneNumber: phone}).then(res => {
					let data = res.data;
					if(data.success) {
						this.$jzUtil.showMsg('发送成功，请查收');
					}
				}).catch(res => {// 失败进行的操作
					console.log(res)
				});
			} else {
				this.$jzUtil.showMsg("请输入正确的手机号");
			}
		},
		checkboxChange(e, vModel){
			let data = this.data[vModel];
			for(let i in data) {
				// #ifdef MP-WEIXIN
				data[i].checked = e.detail.value.indexOf(i.toString()) > -1;
				// #endif
				// #ifdef MP-ALIPAY
				data[i].checked = e.detail.value.indexOf(parseInt(i)) > -1;
				// #endif
			}
			
		},
        radioChange(e, inputItem) {
            this.data = this.values;
            const vModel = inputItem.vModel;
            this.data[vModel] = e.detail.value;
            let item = this.findItem(inputItem.radioGroup, e.detail.value);
            if(!item) {
                this.$jzUtil.showMsg('系统异常，请联系客服');
                return;
            }
            if(item.extObj) {
                this.data = Object.assign(this.data, item.extObj);
            }
            console.log(this.data)
            this.$emit("submit", this.data);
        },
        findItem(items, value) {
		    for(let item of items) {
		        if(item.value === value) {
		            return item;
                }
            }
        }
	}
};
</script>

<style lang="scss">
	.mask {
		position: fixed;
		z-index: 1000;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		visibility: hidden;
		opacity: 0;
		transition: all 0.3s ease;
	}
	.mask.show{
		visibility: visible;
		opacity: 1;
	}
	.modal-box {
		position: absolute;
		top: 20%;
		left: 6%;
		right: 6%;
		width: 88%;
		font-size: $font-base;
		color: $font-color-base;
		border: none;
		z-index: 3000;
		transform: translateY(0);
		.modal-content {
			background-color: #ffffff;
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;
			padding: 0 0 20rpx 0;
			.modal-title {
				width: 100%;
				height: 100rpx;
				font-size: 30rpx;
				font-weight: 500;
				line-height: 100rpx;
				text-align: center;
				color: $font-color-disabled;
				border-bottom: 1rpx solid $border-color-base;
			}
		}
		.cancel-box {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			color: $font-color-disabled;
			.ylh-icon {
				font-size: 48rpx;
			}
		}
	}
	.modal-form {
		text-align: left;
		width: 100%;
		padding: 20rpx 40rpx;
		.modal-form-item {
			display:flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			width:100%;
			//height: 100rpx;
			//line-height: 100rpx;
			padding: 0 40rpx;
			margin: 25rpx 0;
			/*border-bottom: 1px solid $border-color-base;*/
			.ylh-icon {
				color: $primary-color-dark;
				font-size: 42rpx;
				margin-right: $page-row-spacing - 4rpx;
			}
			.modal-form-input {
				height: 80rpx;
				line-height:80rpx;
				background:#FFF;
				flex: 1;
				border-bottom: 1px solid $border-color-base;
			}
			.v-code {
				width: 200rpx;
				color: #ffffff;
				background-color: $font-color-disabled;
				padding:0;
				margin-left: 28rpx;
			}
			.v-code-exp {
				background-color: $primary-color-base;
			}
            .uni-list {
                display: flex;
                width: 100%;
                .uni-list-cell {
                    display: flex;
                    padding: 12rpx 0;
                    .radio-text {
                        padding-left: 8rpx;
                    }
                }
            }
		}
		.modal-form-item-no-height {
			height: unset !important;
		}
		/*.modal-form-item:last-child {*/
		/*	border-bottom: none;*/
		/*}*/
	}
	.modal-bottom {
		width: 100%;
		padding-top: 20rpx;
		border-top: 1px solid $border-color-base;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.action-btn{
		width: 90%;
		border-radius: 12rpx;
		border: none;
		box-sizing: border-box;
		overflow: hidden;
		background-color: $primary-color-base !important;
		color: #fff !important;
	}
	.modal-v-code {
		display: block;
		width: 50rpx;
		height: 50rpx;
	}
	.modal-box .uni-list-cell:last-child::after {
		height: 0rpx; 
	}
	.modal-box .uni-list-cell-last.uni-list-cell::after {
		height: 0rpx; 
	}
	.modal-box .uni-list-cell {
		justify-content: flex-start !important;
	}
	.modal-box .uni-list-cell-pd {
		padding: 0 !important;
	}
	.modal-form-item-checkbox {
		checkbox {
			transform:scale(0.7)
		}
	}
	.modal-form-item-checkbox-label {
		font-size: $font-sm;
		line-height: 40rpx;
	}
	.simple-button {
		font-size: $font-lg + 6rpx;
		text-align: center;
		width: 100%;
		color: $primary-color-base;
	}
</style>
