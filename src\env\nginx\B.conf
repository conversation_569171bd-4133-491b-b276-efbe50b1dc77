	server {
        listen       80;
        server_name  b2.3322a.cn BZ2pVSXyPoCHAihSuXGo66IyrICttgFGrrCLaFdHHnsJ1Hl6Fo2Cd68ZU2YJbtN.3322a.cn;

        #charset koi8-r;

        #access_log  logs/mastertest.log  main;

		root /etc/nginx/beian/3322a/;
    }

	server {
        listen       443 http2 ssl;
        server_name  b2.3322a.cn;
        ssl_certificate      3322a.cn.cer;
        ssl_certificate_key  3322a.cn.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;

		location /wxb603972ba6b8c166 {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://bz2pvsxypochaihsuxgo66iyricttgfgrrclafdhhnsj1hl6fo2cd68zu2yjbtn.3322a.cn/wxb603972ba6b8c166/ permanent;
		}

		location ^~/fwechat/ {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://bz2pvsxypochaihsuxgo66iyricttgfgrrclafdhhnsj1hl6fo2cd68zu2yjbtn.3322a.cn/fwechat/wxb603972ba6b8c166/ permanent;
		}

		location ^~/mgr/ {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://bz2pvsxypochaihsuxgo66iyricttgfgrrclafdhhnsj1hl6fo2cd68zu2yjbtn.3322a.cn/mgr/ permanent;
		}

        location ~* ^/(football|football-g|football-test|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_c;
        }

		location / {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://bz2pvsxypochaihsuxgo66iyricttgfgrrclafdhhnsj1hl6fo2cd68zu2yjbtn.3322a.cn permanent;
		}
	}

	#2180 = yj
	server {
        listen       443 http2 ssl;
        server_name  bz2pvsxypochaihsuxgo66iyricttgfgrrclafdhhnsj1hl6fo2cd68zu2yjbtn.3322a.cn;
        ssl_certificate      3322a.cn.cer;
        ssl_certificate_key  3322a.cn.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;

		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css application/javascript;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";


		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}

		location /wxb603972ba6b8c166 {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://$host/fwechat$1 permanent;
		}

		location ^~/h5/ {
			root /home/<USER>/B/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /h5/wxb603972ba6b8c166/$uri $uri /h5/wxb603972ba6b8c166/index.html;
        }

		location ^~/fwechat/ {
			add_header Cache-Control "max-age=0";
			root /home/<USER>/B/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /fwechat/wxb603972ba6b8c166/$uri $uri /fwechat/wxb603972ba6b8c166/index.html;
        }
		
		location ^~/cashier/ {
			root /home/<USER>/jeepay/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /cashier/$uri $uri /cashier/index.html;
        }
		
		location ^~/api/ {
			proxy_next_upstream http_502 http_504 error timeout invalid_header;
			proxy_set_header Host  $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://pm-api;
			# 启用支持websocket连接
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection "upgrade";
        }
		
        location ~* ^/(football|football-g|football-test|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_c;
        }
		
		location ~* ^/mgr/ {
			root /home/<USER>/B/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /mgr/$uri $uri /mgr/index.html;
        }
		
		location / {
			root /etc/nginx/beian/3322a/;
        }

        #error_page   500 502 503 504  /50x.html;
        #location = /50x.html {
        #    root   html;
        #}
    }
