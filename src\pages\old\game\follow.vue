<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getData(true)">
            <view class="promo-list">
                <view v-for="(item, index) in pageInfo.list" :key="index">
                    <com-promo-series :index="index+1" :item.sync="item" @buyGame="buy(item)"></com-promo-series>
                </view>
            </view>
            <jz-empty v-if="!pageInfo.list || pageInfo.list.length === 0" content="暂无数据"></jz-empty>
        </scroll-view>
        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import comPromoSeries from '../components/com-promo-series'
import uniFab from '@/components/uni-fab'
import {getUserFollowGameList} from '@/common/request/user-game'
import jzEmpty from '@/components/jz-empty';
import promoBuy from "@/pages/home/<USER>/promo-buy";

export default {
    components: {comPromoSeries, uniFab, jzEmpty},
    mixins: [promoBuy],
    data() {
        return {
            pageInfo: {
                list: [],
                loadStatus: 'more',
                params: {
                    current: 0,
                    size: 100,
                },
            }
        }
    },
    onLoad() {
        this.getData()
    },
    methods: {
        getData() {
            getUserFollowGameList(this.pageInfo.params).then(res=> {
                this.pageInfo.list = res.data;
            }).catch(res=>{});
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: #FFFFFF;
    padding-bottom: 20rpx;
}
.series {
    background-color: #fff;
    padding: 10rpx 24rpx 12rpx 14rpx;
    .top-title {
        font-size: $font-lg + 4rpx;
        padding-left: 12rpx;
    }
    .series-memo-box {
        display: flex;
        flex-direction: row;
        padding: 20rpx 12rpx 10rpx 20rpx;
        .series-memo-img {
            image {
                width: 160rpx;
                height: 160rpx;
            }
        }
        .series-memo-box-r {
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            .series-title-box {
                padding: 0 0 12rpx 0;
                .series-title {
                    font-size: $font-lg;
                    font-weight: 700;
                }
                .series-title-memo {
                    font-size: $font-sm;
                    padding-left: 20rpx;
                }
            }
            .series-memo {
                font-size: $font-base;
                text-indent: 2em;
                color: $font-color-light;
            }
        }
    }
}
.promo-reason {
    background-color: #fff;
    .promo-content {
        padding: 10rpx 24rpx;
        font-size: $font-lg;
        background-color: #fff;
        text-indent: 2em;
        color: $font-color-light;
    }
}
</style>