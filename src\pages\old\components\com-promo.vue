<template>
    <view class="com-promo">
        <view class="com-promo-title">
            <text class="ylh-icon icon-check"></text>
            <text>时间：{{item.playTime | date('yyyy-MM-dd hh:mm')}}</text>
        </view>
        <view class="com-promo-name font-red">赛事：{{item.gameName}}</view>
        <view class="com-promo-vs">对阵：{{item.home}} VS {{item.away}}</view>
        <view class="com-promo-score">比分：{{item.score?item.score:'未知'}} <text style="padding-left:32rpx">结果：</text><text class="result" :class="resultClass(item.result)">{{item.result|promoGameResult}}</text></view>
        <view class="com-promo-promotion"><text>推介：</text><rich-text :nodes="item.gameRemark"></rich-text></view>
        <view class="com-promo-series-name">推介项目：<text class="series-name">{{item.seriesName}}</text></view>
    </view>
</template>

<script>
import UniCard from '@/components/uni-card'
import uniLoadMore from '@/components/uni-load-more'
import jzEmpty from '@/components/jz-empty';
import {buyGame} from '@/common/request/order'
import {isEmpty} from "@/util/validate";
import {showMsgConfirm} from "@/common/jz-util";
import {
    getGameItem,
    getTodayGameList,
    getHistoryGameList,
    getGameResultText
} from "@/common/request/promo-series";

export default {
    name: "comPromo",
    components: {UniCard,uniLoadMore,jzEmpty},
    props: {
        item: {
            type: Object,
            default: function(){
                return {};
            }
        },
        dateFlag: false,
        showSeriesName: false,
        showBuyType: false,
    },
    // mounted() {
    //     console.log(this.item)
    // },
    filters: {
        promoGameResult(input) {
            switch (input) {
                case 'W': return '赢';
                case 'D': return '走盘';
                case 'L': return '输';
                default: return '待';
            }
        }
    },
    methods: {
        canBuy(item) {
            const isNotBuy = isEmpty(item.buyTime);
            // if(item.type === '篮球' && isNotBuy) {
            //     return true;
            // }
            let now = new Date();
            let playTime = new Date(item.playTime.replace(/-/g, "/"));
            playTime = new Date(playTime.setHours(playTime.getHours()+2))
            return (playTime > now) && isEmpty(item.result) && isNotBuy
        },
        resultClass(result) {
            switch (result) {
                case 'L': return 'color-green';
                case 'W': return 'color-red';
                default:
                case 'D': return 'color-black';

            }
        },
        buy(game) {
            this.$emit('buyGame', game);
        },
        getCombineGames(games) {
            const gameArray = games.split('\n');
            let combineGamesList = [];
            for(let game of gameArray) {
                const gameContentArray = game.split(",");
                if(gameContentArray.length === 3) {
                    combineGamesList.push({playTime: gameContentArray[0], home:gameContentArray[1], away: gameContentArray[2]});
                }
            }
            return combineGamesList;
        }
    }
}
</script>

<style lang="scss" scoped>
.com-promo {
    font-size: $font-lg;
    background-color: #FFFFFF;
    padding: 12rpx 8rpx 12rpx 24rpx;
    border-bottom: 1px dashed #04BE02;
    .com-promo-title, .com-promo-name, .com-promo-vs, .com-promo-score, .com-promo-promotion, .com-promo-series-name {
        padding: 6rpx 0;
    }
    .com-promo-title {
        .ylh-icon {
            font-size: $font-lg + 8rpx;
        }
    }
    .com-promo-name {
        font-weight: 700;
    }
    .com-promo-score {
        .result {
            font-weight: 700;
        }
    }
    .com-promo-promotion {
        display: flex;
        flex-direction: row;
        color: red;
    }
    .com-promo-series-name {
        .series-name {
            color: #fff;
            background-color: $primary-color-series-name;
            padding: 0 8rpx;
        }
    }
}

.color-red {
    color:red;
}
.color-black {
    color:#000;
}
.color-green {
    color: #08801a;
}
.font-red {
  color:red;
}
</style>
