<template>
    <view class="content">
        <view class="user-info">
            <view class="jz-list-item">
                <view class="left">
                    <view class="img-wrap full-radius">
                        <image :src="accountFans.headimgUrl | headicon" mode="widthFix"></image>
                    </view>
                </view>
                <view class="right">
                    <view class="top" style="align-items: flex-start;height: 70rpx;">
                        <text class="name" style="font-size: 34rpx;" @click="clickUserInfo()">
                            {{accountFans&&accountFans.id?accountFans.nickname:'点击登录'}}
                        </text>
                    </view>
                    <view class="bottom">
                        <view class="level full-radius">
                            金币：{{wallet.coin | number(2)}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="cust-content" v-if="groups.length > 0">
            <uni-section title="充值套餐" type="line"></uni-section>
            <uni-grid :column="3" :highlight="false" :show-border="false" @change="change">
                <uni-grid-item v-for="(item, index) in groups" :index="index" :key="index">
                    <view class="coin-box">
                        <view class="coin-item coin-border full-radius" :class="{'full-border': index === selectedIndex}">
                            <view class="coin-item-row coin-count">
                                <text class="ylh-icon icon-taojinbi"></text>
                                <text>{{item.coinCount}}球币</text>
                            </view>
                            <view class="coin-item-row price">{{item.price}}元</view>
                            <view class="coin-give">
                                <view class="coin-item-row text full-radius">送{{item.give}}球币</view>
                            </view>
                        </view>
                    </view>
                </uni-grid-item>
            </uni-grid>
        </view>
        <view class="x-content">
            <view class="buy-box">
                <view class="buy-price-box">
                    <uni-number-box :min="0.01" :max="9999999999" :step="1" v-model="payPrice"></uni-number-box>
                </view>
                <view class="buy-button">
                    <view class="jz-button" @click="submitCoin">
                        <text v-if="payPrice > 0">微信支付 {{payPrice}}元</text>
                    </view>
                </view>
            </view>
            <uni-section title="充值说明" type="line"></uni-section>
            <com-dict-memo dict-type="coin_charge_memo"></com-dict-memo>
            <view style="padding-bottom:50px"></view>
            <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
        </view>
    </view>
</template>

<script>
import {mapState, mapMutations} from 'vuex';
import uniFab from '@/components/uni-fab'
import UniGrid from '@/components/uni-grid/uni-grid'
import UniGridItem from '@/components/uni-grid/uni-grid-item'
import uniSection from '@/components/uni-section'
import uniNumberBox from '@/components/uni-number-box'
import {getDictListByType} from "@/common/request/admin";
import {submitCoin, submitCoinByUniPay} from "@/common/request/order";
import ComDictMemo from "@/pages/admin/components/com-dict-memo";

export default {
    components: {uniFab,ComDictMemo,UniGrid,UniGridItem,uniSection,uniNumberBox},
    computed: {
        ...mapState({
            userInfo: state => {return state.user.userInfo},
        })
    },
    data() {
        return {
            wallet: {},
            accountFans: {},
            groups: [],
            selectedIndex: -1,
            payPrice: 0.01
        }
    },
    onShow() {
        this.getData();
    },
    methods: {
        getData() {
            const me = this;
            getDictListByType('coin_charge_discount').then((res)=> {
                const data = res.data;
                me.groups = [];
                for(let item of data) {
                    let obj = { price: parseFloat(item.value), give: parseFloat(item.label) };
                    obj.coinCount = obj.price + obj.give;
                    me.groups.push(obj)
                }
                console.log(me.groups)
            }).catch(res=>{});
            this.$store.dispatch('user/getWallet').then((wallet)=> {
                this.wallet = wallet;
            }).catch(res=>{});
            this.$store.dispatch('user/getAccountFans').then((data)=> {
                this.accountFans = data;
            }).catch(res=>{});
        },
        change(e) {
            let { index } = e.detail
            this.selectedIndex = index;
            let item = this.groups[this.selectedIndex];
            this.payPrice = parseFloat(item.price);
        },
        submitCoin() {
            if(this.payPrice > 0) {
              submitCoinByUniPay({coin: this.payPrice}).then(res => {
                    let orderPayDto = res.data;
                    orderPayDto.rtPath = "/customer/info";
                    //console.log(orderPayDto);
                    this.gotoName('pay', orderPayDto);
                }).catch(res => {});
            }
        }
    }
}
</script>

<style lang="scss">
.user-info {
    background-color: #ffffff;
    background-image: url('https://oss.yj-star.com/admin/sys-file/fb-mg/customer-banner.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;/* 如果图片比较小,框比他大的时候,设置的显示方式,repeat-x沿x轴显示,repeat-y沿y轴显示 */
    background-position: left top;/* 设置图片的位置,left top左上,center center居中..... */
    .list {
        height: 100%;
    }
    .jz-list-item {
        background-color: rgba(255, 255, 255, 0.1);
        align-items: flex-start;
        padding: 24rpx $page-row-spacing;
        .left {
            flex: 0 0 18%;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
        }
        .right {
            flex-grow: 1;
            margin-left: $page-row-spacing - 10rpx;
            .top {
                height: 48rpx;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                color: #ffffff;
                .name{
                    font-size: $font-base + 4rpx;
                    font-weight: bold;
                }
            }
            .bottom {
                font-size: $font-base;
                color: #ffffff;
                display: flex;
                justify-content: flex-start;
                .level {
                    padding: 4rpx $page-row-spacing/2;
                    border: 1rpx solid #ffffff
                }
                .charge {
                    margin-left: 12rpx;
                    padding: 8rpx 28rpx;
                    background-color: orange;
                    font-weight: bold;
                }
            }
        }
    }
}
.cust-content {
    //background-color: #fff;
    .promo-search-box {
        padding: 10rpx;
        background-color: $border-color-base;
        border-radius: 2px;
    }
    .coin-border {
        border: 1px solid $border-color-base;
    }
    .coin-box {
        padding: 20rpx 6rpx 20rpx 12rpx;
    }
    .coin-item {
        background-color: #fff;
        border-color: #dc9f44;
        border-width: 4rpx;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: $font-lg;
        padding: 40rpx 0;
        font-weight: 700;
        .coin-item-row {
            height: 80rpx;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
        .ylh-icon {
            font-size: 38rpx;
            color: #e7234e;
        }
        .coin-count {
            height: 60rpx;
        }
        .price {
            color: #dc9f44;
            height: 100rpx;
        }
        .coin-give {
            .text {
                height: 60rpx;
                font-size: $font-base;
                color: #ffffff;
                background-color: #f3d19e;
                padding: 0 20rpx;
                font-weight: 500;
            }
        }

    }
    .full-border {
        background-color: #f3e7d6;
    }
}

.x-content {
    background-color: #fff;
    .buy-box {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx;
        background-color: #fff;
        .buy-price-box {
            flex: 1.4;
            padding: 0 28rpx;
        }
        .buy-button {
            flex: 1;
            height: 80rpx;
            .jz-button {
                font-weight: 700;
                background-color: #04BE02;
                line-height: 80rpx;
                border-radius: 44rpx;
                box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.1);
            }
        }
    }
    .buy-memo-box {
        padding: 20rpx;
        .buy-memo {
            display: flex;
            flex-direction: column;
        }
    }
}
</style>
