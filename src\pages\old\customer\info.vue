<template>
  <view class="content">
    <view class="user-info">
      <view class="jz-list-item">
        <view class="left">
          <view class="img-wrap full-radius">
            <image :src="customerInfo.avatar | headicon" mode="widthFix"></image>
          </view>
        </view>
        <view class="right">
          <view class="top" style="align-items: flex-start;height: 70rpx;">
            <text class="name" style="font-size: 34rpx;" @click="clickUserInfo()">
              {{ customerInfo.nickname }}
            </text>
          </view>
          <view class="bottom">
            <view class="level full-radius">
              球币：{{ coinAmt | number(2) }}
            </view>
            <view class="jz-view-button charge" @click="openPopup()">充&nbsp;值</view>
          </view>
        </view>
      </view>
    </view>
    <scroll-view class="scroll-box" scroll-y @scrolltolower="getUserGameList(true)">
      <view class="cust-content">
        <!--            <view class="pending-box m-t m-b full-radius">-->
        <!--                <view class="pending" @click="$gotoUrl('order/purchase-list?tab=待付款', true)">-->
        <!--                    <view class="name">我的关注</view>-->
        <!--                    <uni-badge class="order-cnt" :text="1" type="error" />-->
        <!--                </view>-->
        <!--                <view class="pending" @click="$gotoUrl('order/purchase-list?tab=待付款', true)">-->
        <!--                    <view class="name">我的方案</view>-->
        <!--                    <uni-badge class="order-cnt" :text="1" type="error" />-->
        <!--                </view>-->
        <!--            </view>-->
        <view class="jz-list-item" hover-class="item-hover" :hover-stay-time="50" @click="goto('/follow/list')">
          <view class="item-title">
            <text class="ylh-icon icon-wodefensi list-icon"></text>
            我的关注
            <view class="order-cnt" v-if="followCnt>0">
              <text>{{ followCnt }}</text>
            </view>
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
        <view class="jz-list-item" hover-class="item-hover" :hover-stay-time="50" @click="goToTab('/ai/jz')">
          <view class="item-title">
            <text class="ylh-icon icon-wodefensi list-icon"></text>
            AI
            <view class="order-cnt" v-if="aiCnt>0">
              <text>{{ aiCnt }}</text>
            </view>
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
        <view class="jz-list-item" hover-class="item-hover" :hover-stay-time="50"
              @click="goto('/customer/balance/list')">
          <view class="item-title">
            <text class="ylh-icon icon-wodefensi list-icon"></text>
            查看金币记录
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
        <view class="jz-list-item" hover-class="item-hover" :hover-stay-time="50"
              @click="gotoName('phoneUpdate')">
          <view class="item-title">
            <text class="ylh-icon icon-wodefensi list-icon"></text>
            绑定手机号
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
<!--        <view v-if="config.isH5" class="jz-list-item" hover-class="item-hover" :hover-stay-time="50"-->
<!--              @click="logout">-->
<!--          <view class="item-title">-->
<!--            <text class="ylh-icon icon-wodefensi list-icon"></text>-->
<!--            退出登录-->
<!--          </view>-->
<!--          <text class="ylh-icon icon-right"></text>-->
<!--        </view>-->
        <view class="jz-list-item bottom-radius b-b" hover-class="item-hover" :hover-stay-time="50"
              v-if="!otherCustomer && customers===true" @click="gotoName('customers')">
          <view class="item-title">
            <text class="ylh-icon icon-wodefensi list-icon"></text>
            切换用户
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
        <view class="jz-list-item bottom-radius b-b" hover-class="item-hover" :hover-stay-time="50"
              v-if="otherCustomer===true" @click="loginMyself">
          <view class="item-title">
            <text class="ylh-icon icon-wodefensi list-icon"></text>
            返回我的账号
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
        <!--            <view class="jz-list-item bottom-radius b-b" hover-class="item-hover" :hover-stay-time="50"-->
        <!--                  @click="goto('/game/add')">-->
        <!--                <view class="item-title">-->
        <!--                    <text class="ylh-icon icon-wodefensi list-icon"></text>-->
        <!--                    新增赛事-->
        <!--                </view>-->
        <!--                <text class="ylh-icon icon-right"></text>-->
        <!--            </view>-->
      </view>
      <view class="promo-list">
        <com-promo :dateFlag="true" :item="item" v-for="(item, index) in pageInfo.list" :key="index"></com-promo>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {mapState, mapMutations} from 'vuex';
import UniBadge from "@/components/uni-badge"
import {getUserFollowGameCount} from "@/common/request/user-game";
import {checkPermission} from "@/common/request/admin";
import store from "@/store";
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";
import {getUserGameList} from '@/common/request/user-game'
import comPromo from '../components/com-promo';
import {isEmpty} from "@/util/validate";
import {getAiCount} from "@/common/request/promo-series";

export default {
  components: {UniBadge, comPromo},
  computed: {
    ...mapState({
      userInfo: state => {
        return state.user.userInfo
      },
      accountFans: state => {
        return state.user.accountFans
      },
      otherCustomer: state => {
        return state.user.other_customer
      }
    }),
    customerInfo() {
      let customerInfo = { nickname: '点击登录', avatar: '' };
      // 优先取社交帐号
      if (this.accountFans && this.accountFans.nickname) {
        customerInfo.nickname = this.accountFans.nickname;
        customerInfo.avatar = this.accountFans.headimgUrl;
      } else if (this.userInfo.username) {
        customerInfo.nickname = this.userInfo.username;
        customerInfo.avatar = this.userInfo.avatar;
      }
      return customerInfo;
    },
  },
  data() {
    return {
      wallet: {},
      //accountFans: {},
      chargeCoinQty: 100,
      followCnt: 0,
      aiCnt: 0,
      coinAmt: 0,
      customers: false,
      pageInfo: {
        list: [],
        loadStatus: 'more',
        params: {
          current: 0,
          size: 8,
        },
      }
    }
  },
  watch: {
    isCustomer(n, o) {
      store.dispatch('user/setOtherCustomer', true);
    }
  },
  onShow() {
    this.getData();
    this.getUserFollowGameCount();
    this.getAiCount();
    this.checkPermission();
    this.getUserGameList(false)
  },
  methods: {
    getData() {
      this.$store.dispatch('user/getWallet').then((wallet) => {
        this.wallet = wallet;
        this.coinAmt = wallet.coin + wallet.backupCoin;
      }).catch(res => {
      });
      if (isEmpty(this.accountFans)) {
        this.$store.dispatch('user/getAccountFans').then((data) => {
        }).catch(res => {
        });
      }

    },
    getUserGameList(nextPage) {
      bindScrollPageInfo(this.pageInfo, nextPage).then(pageInfo => {
        this.pageInfo = pageInfo;
        getUserGameList(this.pageInfo.params).then(res => {
          const data = res.data;
          this.pageInfo = bindPageInfo(this.pageInfo, data)
        }).catch(res => {
        });
      }).catch(() => {
      })
    },
    openPopup() {
      this.gotoName("coinCharge");
      //this.$refs.popup.openPopup()
    },
    getUserFollowGameCount() {
      getUserFollowGameCount().then(res => {
        this.followCnt = res.data;
      }).catch(res => {
      });
    },
    getAiCount() {
      getAiCount().then(res => {
        this.aiCnt = res.data;
      }).catch(res => {
      });
    },
    checkPermission() {
      checkPermission().then(res => {
        this.customers = res.data;
      }).catch(res => {
      });
    },
    loginMyself() {
      store.dispatch('user/removeUserCache');
      this.gotoIndex();
    },
    logout() {
      this.$store.dispatch("user/logout").then(()=> {
        this.gotoIndex();
      });
    }
  }
}
</script>

<style lang="scss">
.content {
  height: 100%;
}

.user-info {
  height: 180rpx;
  background: linear-gradient(45deg, $primary-color-base, $primary-color-light);
  background-size: 100% 100%;

  .list {
    height: 100%;
  }

  .jz-list-item {
    background-color: rgba(255, 255, 255, 0.1);
    align-items: flex-start;
    padding: 24rpx $page-row-spacing;

    .left {
      flex: 0 0 18%;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }

    .right {
      flex-grow: 1;
      margin-left: $page-row-spacing - 10rpx;

      .top {
        height: 48rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        color: #ffffff;

        .name {
          font-size: $font-base + 4rpx;
          font-weight: bold;
        }
      }

      .bottom {
        font-size: $font-base;
        color: #ffffff;
        display: flex;
        justify-content: flex-start;

        .level {
          padding: 4rpx $page-row-spacing/2;
          border: 1rpx solid #ffffff
        }

        .charge {
          margin-left: 12rpx;
          padding: 8rpx 28rpx;
          background-color: orange;
          font-weight: bold;
        }
      }
    }
  }
}

.cust-content {
  padding: 24rpx 0 2rpx 0;

  .item-title {
    align-items: center;
    height: 100%;
    display: flex;

    .list-icon {
      font-size: 50rpx;
    }

    .icon-zhibo {
      transform: scale(0.7);
    }
  }

  button {
    line-height: 55rpx;
    border: none;
    border-radius: 0;
  }

  button:after {
    border: none;
  }
}

.scroll-box {
  height: calc(100% - 180rpx);
  padding: 4rpx 24rpx;

  .promo-list {
    padding-top: 16rpx;
    padding-bottom: 4rpx;
  }

  .com-promo:last-child {
    border-bottom: none;
  }
}

.pending-box {
  width: 100%;
  height: 160upx;
  background-color: #ffffff;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .pending {
    position: relative;
    height: 100%;
    flex: 25%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;

    .icon-box {
      width: 54upx;
      height: 54upx;
      margin-top: 20upx;
      color: $primary-color-light;
      background-color: #ffffff;
      font-size: $font-base;
      display: flex;
      justify-content: center;
      align-items: center;

      .ylh-icon {
        font-size: 60upx;
      }
    }

    .name {
      color: $font-color-light;
      font-size: $font-base;
      margin-bottom: 20upx;
    }
  }

  //.order-cnt {
  //    position: absolute;
  //    top: 10rpx;
  //    right: 30rpx;
  //}
}

.order-cnt {
  margin-left: 8rpx;
  //position: absolute;
  //right: -0.8rem;
  //top: -0.2rem;
  width: 40rpx;
  height: 40rpx;
  background-color: red;
  //border: 1px dashed #fdb00b;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: $font-sm + 4rpx;
  font-weight: bold;
}
</style>
