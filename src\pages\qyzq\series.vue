<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getHistoryGameList(true)">
            <com-promo-series-head :promo-series-id="promoSeriesId"></com-promo-series-head>
            <view class="m-t">
                <uni-section title="最新方案" type="line"></uni-section>
                <view>
                    <com-promo :item="item" v-for="(item, index) in newPromoList" :key="index" @buyGame="buy"></com-promo>
                </view>
            </view>
            <view class="m-t">
                <uni-section title="历史方案" type="line"></uni-section>
                <view class="promo-list">
                    <com-promo :dateFlag="true" :item="item" v-for="(item, index) in historyPromoList" :key="index"></com-promo>
                </view>
            </view>
            <charge ref="popup" title="余额不足，请及时充值"></charge>
            <uni-load-more v-if="pageInfo.size > 0 && historyPromoList.length > 0" :status="loadStatus"></uni-load-more>
        </scroll-view>
        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from './components/com-promo'
import uniFab from '@/components/uni-fab'
import {
    getGameItem,
    getTodayGameList,
    getHistoryGameList,
    getGameResultText
} from "@/common/request/promo-series";
import uniLoadMore from "@/components/uni-load-more";
import {isEmpty} from "@/util/validate";
import promoBuy from "@/pages/home/<USER>/promo-buy";
import charge from "@/pages/customer/components/charge";
import ComPromoSeries from "@/pages/home/<USER>/com-promo-series";
import ComPromoSeriesHead from './components/com-promo-series-head';

export default {
    components: {ComPromoSeries, uniSection, uniCard, comPromo, uniFab, uniLoadMore, charge, ComPromoSeriesHead},
    mixins: [promoBuy],
    data() {
        return {
            rawPay: true,
            results: [],
            promoSeriesId: '',
            game: {},
            newPromoList: [],
            historyPromoList: [],
            items: [],
            loadStatus: 'more',
            pageInfo: {
                current: 0,
                size: 8,
            },
        }
    },
    onLoad() {
        let options = this.getParams();
        console.log('game onload', options);
        this.promoSeriesId = options.id;
        if(this.promoSeriesId) {
            this.getTodayGameList();
            this.getHistoryGameList();
        }
    },
    methods: {
        getTodayGameList() {
            getTodayGameList({promoSeriesId: this.promoSeriesId,noResult:false}).then(res=> {
                this.newPromoList = res.data;
            }).catch(res=>{});
        },
        getHistoryGameList(nextPage) {
            if(nextPage) {
                this.pageInfo.current += 1;
            } else {
                this.loadStatus = 'more';
                this.historyPromoList = [];
                this.pageInfo.current = 1;
            }
            if(this.loadStatus !== 'more') {
                return;
            }
            this.pageInfo.promoSeriesId = this.promoSeriesId;
            uni.showLoading();
            getHistoryGameList(this.pageInfo).then(res=> {
                const data = res.data.records;
                if(!isEmpty(data)) {
                    this.historyPromoList = this.historyPromoList.concat(data);
                }
                if(!data || data.length < this.pageInfo.size) {
                    this.loadStatus = 'noMore'
                }
                uni.hideLoading();
            }).catch(res=>{uni.hideLoading()});
        },
        getResults() {
            const me = this;
            getGameResultText(me.promoSeriesId).then(res=> {
                if(res.data) {
                    me.results = res.data.split('');
                    let times = 0;
                    me.winHistory.times = me.results.length;
                    for(let i = 0; i < me.results.length; i++) {
                        if(this.results[i]==='W') {
                            times++;
                            me.winHistory.winTimes++;
                        }
                    }
                    if(times > 0) {
                        me.winTimes = times+'连胜';
                    }
                }
            }).catch(res=>{});
        },
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
}
</style>
