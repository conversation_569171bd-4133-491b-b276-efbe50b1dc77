import {get, getByService, post} from "@/common/request/jz-request";

export const getGamePromo = id => get('/gamepromo/' + id);
export const addGamePromo = params => post('/gamepromo', params);
export const editGamePromo = params => post('/gamepromo', params);



export const getGamePromoSeriesList = query => get('/gamepromoseries/page', query);


export const getDictListByType = type => getByService('admin', '/dict/type/' + type);
export const getDictListByTypeAndTenantId = (type, originTenantId) => getByService('admin', '/dict/type/backup/' + type, {originTenantId: originTenantId});
export const getCustomers = nickName => getByService('admin', '/wxaccountfans/getMyFans', {nickName: nickName});
export const getMyFansToken = openId => getByService('admin', '/wxaccountfans/getMyFansToken', {openid: openId});
export const checkPermission = () => getByService('admin', '/wxaccountfans/checkPermission');
export const getPayHistoryList = params => get('/backup/getPayHistoryList', params);
export const getBackupUsers = params => get('/backup/page', params);
export const getDashboard = () => get('/backup/getDashboard');
export const addBackupUser = params => post('/backup/add', params);
export const removeBackupUser = params => post('/backup/remove', params);
export const changeDashboardData = params => post('/backup/changeDashboardData', params);
export const changePayTenantId = () => post('/backup/changePayTenantId', {});