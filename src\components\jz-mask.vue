<template>
  <view class="jz-mask" v-show="visible">
    <slot name="content"></slot>
    <image v-if="showIcon" :src="$website.ossDomain + '/waiting.gif'"></image>
    <text v-if="message" class="waiting-text">{{message}}</text>
  </view>
</template>

<script>
  export default {
    name: 'jzMask',
    props:{
      visible: {
        type: <PERSON>olean,
        default: false
      },
      showIcon: {
        type: Boolean,
        default: false
      },
      message: {
        type: String,
        default: ''
      }
    }
  }
</script>

<style lang="scss">
.jz-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.2);
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  & image {
    width: 160rpx;
    height: 160rpx;
  }
  .waiting-text{
    margin-top: 20rpx;
    color: $primary-color-base;
    font-size: $font-lg + 8rpx;
  }
}
</style>
