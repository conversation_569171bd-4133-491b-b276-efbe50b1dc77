<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getData(true)">
            <view class="m-t" v-for="(item, index) in pageInfo.list" :key="index">
                <uni-card isShadow mode="basic" title="" :show-footer="false">
                    <view class="com-card-content">
                        <view class="jz-row">
                            <view>日期：{{item.createdTime | date('yyyy-MM-dd hh:mm:ss')}}</view>
                        </view>
                        <view class="jz-row">
                            <view>金币：{{item.changeCoin|noPlusMinusNumber(0)}}个</view>
                        </view>
                        <view class="jz-row">
                            {{item.remark}}
                        </view>
                    </view>
                </uni-card>
            </view>
        </scroll-view>
    </view>
</template>

<script>
import uniCard from '@/components/uni-card'
import uniFab from '@/components/uni-fab'
import jzEmpty from '@/components/jz-empty';
import uniLoadMore from '@/components/uni-load-more'
import {getBalanceHistory} from '@/common/request/customer'
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";

export default {
    components: {uniCard, uniFab, jzEmpty, uniLoadMore},
    data() {
        return {
            tabCurrent: '全部',
            tabs: ['全部', '充值', '支出'],
            pageInfo: {
                list: [],
                loadStatus: 'more',
                params: {
                    current: 0,
                    size: 8,
                },
            }
        }
    },
    onShow() {
        this.getData(false, '全部');
    },
    methods: {
        getData(nextPage, changeType) {
            bindScrollPageInfo(this.pageInfo, nextPage).then(pageInfo => {
                this.pageInfo = pageInfo;
                if(changeType && changeType !== '全部') {
                    this.pageInfo.params = Object.assign(this.pageInfo.params, {changeType: changeType})
                }
                else {
                    this.pageInfo.params.changeType = null;
                }
                getBalanceHistory(this.pageInfo.params).then(res=> {
                    const data = res.data;
                    this.pageInfo = bindPageInfo(this.pageInfo, data)
                }).catch(res=>{});
            }).catch(()=> {})
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
    padding: 16rpx 16rpx 4rpx 16rpx;
    .jz-row {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 16rpx 0;
    }
}
.uni-card {
    margin: 0;
}
.com-card-content {
    width: 100%;
    padding: 16rpx 24rpx;
    font-size: $font-lg;
}
</style>