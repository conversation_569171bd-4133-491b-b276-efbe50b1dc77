	server {
        listen       80;
        server_name  j.3322a.cn jxnkkqrymruepqsveuhevytabz1o3coeowgd4k97krjanrssme5auac1efnsnpu.3322a.cn;

        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		root /etc/nginx/beian/3322a/;
		
		location ~* ^/wechat {
			root /etc/nginx;
			index  index.html index.htm;
			proxy_set_header  X-Real-IP  $remote_addr;
			proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header  Host $http_host;
			proxy_read_timeout 300;
		}
    }
	server {
        listen       443 http2 ssl;
        server_name  j.3322a.cn;
        ssl_certificate      3322a.cn.cer;
        ssl_certificate_key  3322a.cn.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;

		location /wx1c8944e7143c3b93 {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://jxnkkqrymruepqsveuhevytabz1o3coeowgd4k97krjanrssme5auac1efnsnpu.3322a.cn/wx1c8944e7143c3b93/ permanent;
		}

		location ^~/wechat/ {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://jxnkkqrymruepqsveuhevytabz1o3coeowgd4k97krjanrssme5auac1efnsnpu.3322a.cn/wechat/wx1c8944e7143c3b93/ permanent;
		}

		location ^~/mgr/ {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://jxnkkqrymruepqsveuhevytabz1o3coeowgd4k97krjanrssme5auac1efnsnpu.3322a.cn/mgr/ permanent;
		}

        location ~* ^/(theater|theater1|theater-test|football|football-g|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_j;
        }

		location / {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://jxnkkqrymruepqsveuhevytabz1o3coeowgd4k97krjanrssme5auac1efnsnpu.3322a.cn permanent;
		}
	}
	server {
        listen       443 http2 ssl;
        server_name  jxnkkqrymruepqsveuhevytabz1o3coeowgd4k97krjanrssme5auac1efnsnpu.3322a.cn;
        ssl_certificate      3322a.cn.cer;
        ssl_certificate_key  3322a.cn.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css application/javascript;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
		#root /home/<USER>/G/jstar-ui/;
		#root /home/<USER>/ui/front/win08/;
		
		
		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}
		
		location /wx1c8944e7143c3b93 {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://$host/wechat$1 permanent;
		}
		
		location ^~/h5/ {
			root /home/<USER>/J/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /h5/wx1c8944e7143c3b93/$uri $uri /h5/wx1c8944e7143c3b93/index.html;
        }
		
		location ^~/wechat/ {
			add_header Cache-Control "max-age=0";
			root /home/<USER>/J/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /wechat/wx1c8944e7143c3b93/$uri $uri /wechat/wx1c8944e7143c3b93/index.html;
        }
		
        location ~* ^/(theater|theater1|theater-test|football|football-g|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_j;
        }
		
		#location ~* /mgr/\.(gif|jpg|png|css|js|ttf|woff) {
		#	root /home/<USER>/G/;
		#	proxy_read_timeout 300;
		#	#try_files /mgr/$uri $uri 404;
		#}
		
		location ~* ^/mgr/ {
			add_header Cache-Control "max-age=0";
			root /home/<USER>/J/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /mgr/$uri $uri /mgr/index.html;
        }
		
		location / {
			root /etc/nginx/beian/3322a/;
        }

        #error_page   500 502 503 504  /50x.html;
        #location = /50x.html {
        #    root   html;
        #}
    }
