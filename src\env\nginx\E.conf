	server {
        listen       443 http2 ssl;
        server_name  e.qyqy268.com;
        ssl_certificate      qyqy268.com.cer;
        ssl_certificate_key  qyqy268.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;

        location /wx65238bccf6625005 {
            add_header Cache-Control "max-age=0";
            rewrite ^(.*)$  https://eafb7dit2brujc4jqx5qr6kwjofxgrvwymjppfcmb1tnamspdjuimcyftvv3d6o.qyqy268.com/wx65238bccf6625005/ permanent;
        }

        location ^~/bwechat/ {
            add_header Cache-Control "max-age=0";
            rewrite ^(.*)$  https://eafb7dit2brujc4jqx5qr6kwjofxgrvwymjppfcmb1tnamspdjuimcyftvv3d6o.qyqy268.com/bwechat/wx65238bccf6625005/ permanent;
        }

		location ^~/mgr/ {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://eafb7dit2brujc4jqx5qr6kwjofxgrvwymjppfcmb1tnamspdjuimcyftvv3d6o.qyqy268.com/mgr/ permanent;
		}

        location ~* ^/(football|football-g|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_g;
        }

        location / {
            add_header Cache-Control "max-age=0";
            rewrite ^(.*)$  https://eafb7dit2brujc4jqx5qr6kwjofxgrvwymjppfcmb1tnamspdjuimcyftvv3d6o.qyqy268.com/ permanent;
        }
    }
	server {
        listen       443 http2 ssl;
        server_name  eafb7dit2brujc4jqx5qr6kwjofxgrvwymjppfcmb1tnamspdjuimcyftvv3d6o.qyqy268.com;
        ssl_certificate      qyqy268.com.cer;
        ssl_certificate_key  qyqy268.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css application/javascript;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
		#root /home/<USER>/G/jstar-ui/;
		#root /home/<USER>/ui/front/win08/;
		
		#location ^~/wechat/ {
		#	root /home/<USER>/G/;
		#	index  index.html index.htm;
        #    proxy_set_header  X-Real-IP  $remote_addr;
        #    proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        #    proxy_set_header  Host $http_host;
        #    proxy_read_timeout 300;
		#	try_files /wechat/wx0883d85dfbfc233c/$uri/ $uri /wechat/wx65238bccf6625005/index.html;
        #}
		
		
		
		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}
		
		location /wx65238bccf6625005 {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://$host/bwechat$1 permanent;
		}
		
		location ^~/h5/ {
			add_header Cache-Control "max-age=0";
			root /home/<USER>/E/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /h5/wx65238bccf6625005/$uri $uri /h5/wx65238bccf6625005/index.html;
        }
		
		location ^~/bwechat/ {
			add_header Cache-Control "max-age=0";
			root /home/<USER>/E/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /bwechat/wx65238bccf6625005/$uri $uri /bwechat/wx65238bccf6625005/index.html;
        }
		
        location ~* ^/(football|football-g|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://stream_g;
			
# 			set $backend stream_g;
# 			if ($remote_addr = "*************") {
#                 set $backend stream_gray;
#             }
#             proxy_pass http://$backend;
        }
		
		#location ~* /mgr/\.(gif|jpg|png|css|js|ttf|woff) {
		#	root /home/<USER>/G/;
		#	proxy_read_timeout 300;
		#	#try_files /mgr/$uri $uri 404;
		#}
		
		location ~* ^/mgr/ {
			root /home/<USER>/E/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /mgr/$uri $uri /mgr/index.html;
        }
		
		location / {
			root /etc/nginx/beian/3322a/;
        }

        #error_page   500 502 503 504  /50x.html;
        #location = /50x.html {
        #    root   html;
        #}
    }
