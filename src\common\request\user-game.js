import {get} from "@/common/request/jz-request";
import {config} from "@/common/jz-constant";

export const getUserGameList = params => get('/usergame/list', params)

export const getUserFollowGameList = (data) => get('/gamepromoseries/follow/list', data)
export const getUserFollowGameCount = () => get('/gamefollow/count')
export const getGamePromoItemAfterPay = (tradeNo, checkCode) => get('/gamepromo/item/afterPay/' + tradeNo + "?checkCode=" + checkCode)