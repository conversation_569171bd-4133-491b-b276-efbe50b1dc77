<template>
    <view class="content">
        <uni-search-bar placeholder="搜索推介" clearButton="auto" @confirm="search" @clear="searchClear"></uni-search-bar>
        <!--搜索-->
        <view class="promo-search">
            <com-promo-search @search="orderQuery"></com-promo-search>
        </view>
        <view class="notice-box">
            <text class="ylh-icon icon-voice"></text>
            <swiper class="swiper-notice"  vertical="true" autoplay="false" duration="500" interval="4000">
                <swiper-item v-for="(item, index) in notices" :key="index">
                    <text class="notice-text">{{item.textContent}}</text>
                </swiper-item>
            </swiper>
        </view>
        <scroll-view class="list" scroll-y @scrolltolower="getData(true)">
<!--          <zmm-watermark watermark="123"></zmm-watermark>-->
            <!--<uni-section title="今日推介" type="line"></uni-section>-->
            <view class="promo-list">
                <view v-for="(item, index) in promoList" :key="index" v-if="item.lastGamePromo">
                    <com-promo-series :index="index+1" :item.sync="item" :week-rate.sync="weekRateFlag" @buyGame="buy(item.lastGamePromo)"></com-promo-series>
                </view>
            </view>
            <uni-load-more v-if="queryParams.size > 0 && promoList.length > 0" :status="loadStatus"></uni-load-more>
            <charge ref="popup" title="余额不足，请及时充值"></charge>
        </scroll-view>
<!--        <view>-->
<!--            <custom-tab-bar direction="vertical" :show-icon="false" :selected="true" @onTabItemTap="onTabItemTap" />-->
<!--        </view>-->
    </view>
</template>

<script>
import uniLoadMore from "@/components/uni-load-more";
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromoSeries from './components/com-promo-series'
import comPromoSearch from './components/com-promo-search'
import zmmWatermark from '@/components/zmm-watermark'
import {getSimpleAllList,getSeriesGame} from '@/common/request/promo-series'
import {getAdItemsByType} from '@/common/request/ad'
import promoBuy from '../home/<USER>/promo-buy'
import charge from "../customer/components/charge";
import uniSearchBar from '@/components/uni-search-bar'
import fuiLoading from '@/components/fui-loading'
import {getDictListByType} from "@/common/request/admin";
import {isEmpty} from "@/util/validate";

export default {
    components: {uniSection,uniCard,comPromoSeries,charge,uniSearchBar,comPromoSearch,fuiLoading,uniLoadMore,zmmWatermark},
    mixins: [promoBuy],
    data() {
        return {
            promoList: [],
            notices: [],
            groups: [],
            seriesHasGameMap: {},
            loadStatus: 'more',
            loadFlag: false,
            weekRateFlag: true,
            queryParams: {
                current: 0,
                size: 8,
                keywords: "",
                //type: "足球",
                orderColumn: undefined,
                orderAsc: false
            }
        }
    },
    onLoad() {
        this.getSimpleAllList();
        this.getData();
    },
    onShow() {
        this.getTitle();
    },
    methods: {
        getTitle() {
            getDictListByType('wechat_index_title').then((res)=> {
                if(res.data && res.data.length > 0) {
                    const title = res.data[0].value;
                    if(title) {
                        uni.setNavigationBarTitle({
                            title: title
                        })
                    }

                }

            }).catch(res=>{});

        },
        getData(nextPage) {
            if(this.loadFlag){
                return;
            }
            this.loadFlag = true;
            if(nextPage) {
                this.queryParams.current += 1;
            } else {
                this.loadStatus = 'more';
                this.promoList = [];
                this.queryParams.current = 1;
            }
            if(this.loadStatus !== 'more') {
                this.loadFlag = false;
                return;
            }
            uni.showLoading();
            getSeriesGame(this.queryParams).then(res=> {
                this.loadFlag = false;
                uni.hideLoading();
                const data = res.data;
                if(!isEmpty(data)) {
                    this.promoList = this.promoList.concat(data);
                }
                if(!data || data.length < this.queryParams.size) {
                    this.loadStatus = 'noMore'
                }
            }).catch(res=>{
                this.loadFlag = false;
                uni.hideLoading();
            });
        },
        getSimpleAllList() {
            this.groups = [];
            getAdItemsByType(1).then(res=> {
                this.notices = res.data;
            }).catch(res=>{});
        },
        search(res) {
            // uni.showToast({
            //     title: '搜索：' + res.value,
            //     icon: 'none'
            // });
            this.queryParams.keywords = res.value;
            this.getData();
        },
        searchClear() {
            this.queryParams.keywords = null;
            this.getData();
        },
        getSearchParams() {
            let params = { orderColumn: 'hasPromotion' }
        },
        orderQuery(orderParam) {
            const orderColumn = orderParam.orderColumn;
            this.weekRateFlag = !(orderColumn === 'monthRate' || orderColumn.indexOf('month_rate') > -1);
            //console.log(this.weekRateFlag, orderColumn.indexOf('month_rate') > -1)
            this.queryParams.orderColumn = orderParam.orderColumn;
            this.queryParams.orderAsc = orderParam.orderAsc;
            this.getData();
        }
    }
}
</script>

<style lang="scss">
.content {
    height: 100%;
}
.list {
    height: calc(100% - 302rpx);
    padding-bottom: 4rpx;
    background-color: $primary-color-white;
}
//.promo-search {
//    //margin: 0 30rpx;
//    margin-top: 4rpx;
//    box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.1);
//    border: 1px solid $border-color-light;
//}
.com-title {
    width: 100%;
    .ylh-icon{
        color: $primary-color-light;
        font-size: $font-base -2rpx;
        margin-right: $page-row-spacing - 4rpx;
    }
}
.uni-searchbar {
    background-color: $primary-color-base;
}
.swiper-top {
    height: 358rpx !important;;
    image {
        width: 100%;
        height: 100%;
    }
}
.notice-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 18rpx;
    background-color: #fff;
    .icon-voice {
        width: 44rpx;
    }
    .swiper-notice {
        flex: auto;
        height: 62rpx;
        line-height: 62rpx;
    }
    .notice-text {
        color: #dd524d;
    }
}
</style>
