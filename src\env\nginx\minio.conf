upstream minio{
	server localhost:9000;
}
server {
	listen       9000;
	server_name  mio.yj-star.com;
	
	gzip on;
	gzip_buffers 32 4K;
	gzip_comp_level 6;
	gzip_min_length 100;
	gzip_types application/javascript text/css text/xml;
	gzip_disable "MSIE [1-6]\."; #配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
	gzip_vary on;

	location / {
	proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header  Host $http_host;
		proxy_redirect off;
		proxy_read_timeout 300;
		proxy_pass http://minio;
	}
}