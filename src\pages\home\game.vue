<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getHistoryGameList(true)">
            <view class="series">
                <view class="top-title" v-if="!isEmpty(gameId)">
                    {{game.gameName}}
                </view>
                <view class="series-memo-box">
                    <view class="series-memo-img" align="center">
                        <image :src="game.image|productImg" mode="widthFix"/>
                    </view>
                    <view class="series-memo-box-r">
                        <view class="series-title-box">
                            <text class="series-title">{{game.seriesName}}</text>
<!--                            <text class="series-title-memo">知名美女主播</text>-->
                        </view>
                        <view class="series-memo">
                            {{game.seriesPromotion}}
                        </view>
                    </view>
                </view>
            </view>
            <view class="promo-reason m-t" v-if="!isEmpty(game.gameRemark)">
                <uni-section title="推荐理由" type="line"></uni-section>
                <view class="promo-content-combine">
                    <rich-text :nodes="game.gameRemark"></rich-text>
                    <view class="mention">
                        观点仅供参考，投注需谨慎！本观点仅服务于购买中国竞彩以及北京单场的用户，购彩请前往彩票店。
                    </view>
                </view>
            </view>
            <view class="m-t">
                <uni-section title="最近成绩" type="line"></uni-section>
                <view>
                    <com-promo-series-history :series-id="promoSeriesId"></com-promo-series-history>
                </view>
            </view>
            <view class="m-t">
                <uni-section :title="gameId ? '本专区在售方案' : '最新方案'" type="line"></uni-section>
                <view>
                    <com-promo :item="item" v-for="(item, index) in newPromoList" :key="index" @buyGame="buy"></com-promo>
                </view>
            </view>
            <view class="m-t">
                <uni-section title="历史方案" type="line"></uni-section>
                <view class="promo-list">
                    <com-promo :dateFlag="true" :item="item" v-for="(item, index) in pageInfo.list" :key="index"></com-promo>
                </view>
            </view>
            <charge ref="popup" title="余额不足，请及时充值"></charge>
            <uni-load-more v-if="pageInfo.params.size > 0 && pageInfo.list.length > 0" :status="pageInfo.loadStatus"></uni-load-more>
        </scroll-view>
        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from './components/com-promo'
import uniFab from '@/components/uni-fab'
import uniLoadMore from '@/components/uni-load-more'
import comPromoSeriesHistory from './components/com-promo-series-history'
import {getGameItem,getTodayGameList,getHistoryGameList,getSeriesId} from "@/common/request/promo-series";
import {isEmpty} from "@/util/validate";
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";
import promoBuy from "@/pages/home/<USER>/promo-buy";
import charge from "@/pages/customer/components/charge";
import website from "@/common/website";

export default {
    components: {uniSection, uniCard, comPromo, uniFab, uniLoadMore, comPromoSeriesHistory,charge},
    mixins: [promoBuy],
    data() {
        return {
            gameId: '',
            promoSeriesId: '',
            game: {},
            newPromoList: [],
            pageInfo: {
                list: [],
                loadStatus: 'more',
                params: {
                    current: 0,
                    size: 8,
                },
            }

        }
    },
    onLoad() {
        let options = this.getParams();
        console.log('game onload', options);
        this.gameId = options.id;
        this.promoSeriesId = options.promoSeriesId;
        if(this.gameId) {
            this.getGameItem();
        }
        if(this.promoSeriesId) {
            this.getSeriesById();
            this.getTodayGameList();
            this.getHistoryGameList();
        }
    },
    methods: {
        getSeriesById() {
            getSeriesId(this.promoSeriesId).then(res=> {
                const series = res.data;
                this.game.image = series.image;
                this.game.seriesName = series.name;
                this.game.seriesPromotion = series.promotion;
            }).catch(res=>{});
        },
        getGameItem() {
            getGameItem({id: this.gameId}).then(res=> {
                res.data.gameRemark = res.data.gameRemark.replace('/admin/', website.ossDomain + '/admin/')
                this.game = res.data;
            }).catch(res=>{});
        },
        getTodayGameList() {
            getTodayGameList({id: this.gameId,promoSeriesId:this.promoSeriesId,noResult:true}).then(res=> {
                this.newPromoList = res.data;
            }).catch(res=>{});
        },
        getHistoryGameList(nextPage) {
            bindScrollPageInfo(this.pageInfo, nextPage).then(pageInfo => {
                this.pageInfo = pageInfo;
                this.pageInfo.params.promoSeriesId = this.promoSeriesId;
                uni.showLoading();
                getHistoryGameList(this.pageInfo.params).then(res=> {
                    const data = res.data.records;
                    this.pageInfo = bindPageInfo(this.pageInfo, data)
                    uni.hideLoading();
                }).catch(res=>{uni.hideLoading()});
            }).catch(()=> {})
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
}
.series {
    background-color: #fff;
    padding: 10rpx 24rpx 12rpx 14rpx;
    .top-title {
        font-size: $font-lg + 4rpx;
        padding-left: 12rpx;
        font-weight: 700;
    }
    .series-memo-box {
        display: flex;
        flex-direction: row;
        padding: 20rpx 12rpx 10rpx 20rpx;
        .series-memo-img {
            image {
                width: 160rpx;
                height: 160rpx;
            }
        }
        .series-memo-box-r {
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            .series-title-box {
                padding: 0 0 12rpx 0;
                .series-title {
                    font-size: $font-lg;
                    font-weight: 700;
                }
                .series-title-memo {
                    font-size: $font-sm;
                    padding-left: 20rpx;
                }
            }
            .series-memo {
                font-size: $font-base;
                text-indent: 2em;
                color: $font-color-light;
            }
        }
    }
}
.promo-reason {
    background-color: #fff;
    .promo-content {
        padding: 10rpx 24rpx;
        font-size: $font-lg;
        background-color: #fff;
        text-indent: 2em;
        color: $font-color-light;
    }
    .promo-content-combine {
        padding: 24rpx;
        line-height: 48rpx;
        .mention {
            padding-top: 24rpx;
            color: $font-color-disabled;
            font-size: $font-sm;
        }
    }
}
</style>
