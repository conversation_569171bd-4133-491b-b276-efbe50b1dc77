/* #ifndef APP-NVUE */
page,
	/* #ifdef H5 */
.container *,
	/* #endif */
view,
textarea,
button {
	display: flex;
	box-sizing: border-box;
}

page {
	height: 100%;
	width: 100%;
}

.send-btn-box {
	display: flex;
	align-items: flex-end;
}

/* #endif */

.stop-box {
	width: 100%;
	position: absolute;
	bottom: 108rpx;
	height: 30px;
	background: transparent;
}
.stop-responding {
	font-size: 14px;
	border-radius: 3px;
	margin-bottom: 15px;
	background-color: #f0b00a;
	color: #FFF;
	width: 90px;
	height: 30px;
	line-height: 30px;
	margin: 0 auto;
	justify-content: center;
	margin-bottom: 15px;
	/* #ifdef H5 */
	cursor: pointer;
	/* #endif */
}

.stop-responding:hover {
	box-shadow: 0 0 10px #aaa;
}

.container {
	height: 100%;
	background-color: #FAFAFA;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	// border: 1px solid blue;
}

.foot-box {
	width: 750rpx;
	display: flex;
	flex-direction: column;
	padding: 0;
	background-color: transparent;
}

.foot-box-content {
	justify-content: space-around;
	padding: 10px 0;
	background: #fff;
}

.textarea {
	padding: 0;
}
.textarea-box {
	padding: 8rpx 10rpx;
	background-color: #f9f9f9;
	border-radius: 5px;
}

.textarea-box .textarea {
	max-height: 240rpx;
	font-size: 14px;
	/* #ifndef APP-NVUE */
	overflow: auto;
	/* #endif */
	width: 450rpx;
	font-size: 14px;
}

/* #ifdef H5 */
/*隐藏滚动条*/
.textarea-box .textarea::-webkit-scrollbar {
	width: 0;
}

/* #endif */

.input-placeholder {
	color: #bbb;
	line-height: 18px;
}

.trash,
.send {
	width: 50px;
	height: 30px;
	justify-content: center;
	align-items: center;
	flex-shrink: 0;
}

.trash {
	width: 30rpx;
	margin-left: 10rpx;
}

.menu {
	justify-content: center;
	align-items: flex-end;
	flex-shrink: 0;
}

.menu-item {
	width: 30rpx;
	margin: 0 10rpx;
}

.send {
	color: #FFF;
	border-radius: 4px;
	display: flex;
	margin: 0;
	padding: 0;
	font-size: 14px;
	margin-right: 20rpx;
}

/* #ifndef APP-NVUE */
.send::after {
	display: none;
}

/* #endif */


.msg-list {
	height: 0; //不可省略，先设置为0 再由flex: 1;撑开才是一个滚动容器
	flex: 1;
	// border: 1px solid red;
}

.noData {
	margin-top: 15px;
	text-align: center;
	width: 750rpx;
	color: #aaa;
	font-size: 12px;
	justify-content: center;
}

.open-ad-btn-box{
	justify-content: center;
	margin: 10px 0;
}

.tip-ai-ing {
	align-items: center;
	flex-direction: column;
	font-size: 14px;
	color: #919396;
	padding: 15px 0;
}

.uni-link {
	margin-left: 5px;
	line-height: 20px;
}

/* #ifdef H5 */
@media screen and (min-width:650px) {
	.foot-box {
		border-top: solid 1px #dde0e2;
	}

	.container,.container * {
		max-width: 950px;
	}

	.container {
		box-shadow: 0 0 5px #e0e1e7;
		height: calc(100vh - 44px);
		margin: 22px auto;
		border-radius: 10px;
		overflow: hidden;
		background-color: #FAFAFA;
	}

	page {
		background-color: #efefef;
	}

	.container .header {
		height: 44px;
		line-height: 44px;
		border-bottom: 1px solid #F0F0F0;
		width: 100vw;
		justify-content: center;
		font-weight: 500;
	}

	.content {
		background-color: #f9f9f9;
		position: relative;
		max-width: 90%;
	}

	// .copy {
	// 	color: #888888;
	// 	position: absolute;
	// 	right: 8px;
	// 	top: 8px;
	// 	font-size: 12px;
	// 	cursor:pointer;
	// }
	// .copy :hover{
	// 	color: #4b9e5f;
	// }

	.foot-box,
	.foot-box-content,
	.msg-list,
	.msg-item,
		// .create_time,
	.noData,
	.textarea-box,
	.textarea,
	textarea-box {
		width: 100% !important;
	}

	.textarea-box,
	.textarea,
	textarea,
	textarea-box {
		height: 120px;
	}

	.foot-box,
	.textarea-box {
		background-color: #FFF;
	}

	.foot-box-content {
		flex-direction: column;
		justify-content: center;
		align-items: flex-end;
		padding-bottom: 0;
	}

	.pc-menu {
		padding: 0 10px;
	}

	.pc-menu-item {
		height: 20px;
		justify-content: center;
		align-items: center;
		align-content: center;
		display: flex;
		margin-right: 10px;
		cursor: pointer;
	}

	.pc-trash {
		opacity: 0.8;
	}

	.pc-trash image {
		height: 15px;
	}


	.textarea-box,
	.textarea-box * {
		// border: 1px solid #000;
	}
	.send-btn-box .send-btn-tip {
		color: #919396;
		margin-right: 8px;
		font-size: 12px;
		line-height: 28px;
	}
}
/* #endif */
.retries-box{
	justify-content: center;
	align-items: center;
	font-size: 12px;
	color: #d2071b;
}
.retries-icon{
	margin-top: 1px;
	margin-left: 5px;
}