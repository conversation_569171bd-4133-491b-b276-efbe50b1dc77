<template>
	<view class="list-tab-box">
		<view class="list-tab" :class="{ 'list-tab-selected': current === tab }" v-for="(tab, index) in tabs" :key="index" @click="click(tab)" >
			{{tab}}
		</view>
	</view>
</template>

<script>

export default {
	name: "list-tab",
	props: {
		tabs: {
			type: Array,
			default: []
		},
		tabCurrent: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			current: this.tabCurrent,
            tabs_: this.tabs,
		}
	},
    mounted() {
		setTimeout(()=>{
			this.click(this.tabCurrent);
		},100);
    },
	computed: {},
	methods: {
		click(clickTab) {
		    if(this.tabs_.indexOf(clickTab) > -1) {
                this.current = clickTab;
                this.$emit('click', clickTab);
            }
		}
	}
};
</script>

<style lang="scss">
	.list-tab-box {
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
		padding: 8upx;
		background: #FFF;
		border-radius: 64upx;
		.list-tab {
			flex: 1;
			height: 68upx;
			display: flex;
			justify-content: center;
			align-items: center;

			background: transparent;
			color: #333;
			font-weight:400;
		}
		.list-tab-selected {
			background: $primary-color-base;
			border-radius: 64upx;
			color: #ffffff;
			font-weight: 500;
		}
		
	}
</style>
