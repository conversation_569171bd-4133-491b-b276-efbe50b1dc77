import wechat from './wechat.js'

export default {
    name: 'wechatPay',
    data() {
        return {
            orderPayInfo: {}
        };
    },
    methods: {
        xxxPay() {
            const me = this;
            //console.log('pay x1 ', me.orderPayInfo)
            const productId = this.orderPayInfo.productId;
            const promoSeriesId = this.orderPayInfo.promoSeriesId;
            const data = this.orderPayInfo.wxPayMpOrderResult;
            const rtPath = this.orderPayInfo.rtPath;
            const userId = this.orderPayInfo.userId;
            WeixinJSBridge.invoke(
                'getBrandWCPayRequest', {
                    "appId":  data.appId,     //公众号ID，由商户传入
                    "timeStamp":  data.timeStamp,         //时间戳，自1970年以来的秒数
                    "nonceStr": data.nonceStr, //随机串
                    "package": data.packageValue,
                    "signType": data.signType,         //微信签名方式：
                    "paySign": data.paySign //微信签名
                },
                function(res){
                    //alert(JSON.stringify(res))
                    if(res.err_msg == "get_brand_wcpay_request:ok" ){
                        // 使用以上方式判断前端返回,微信团队郑重提示：
                        //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
                        if(productId) {
                            me.goBack();
                            //me.$Router.replace({path: '/game', query: {id: productId, promoSeriesId: promoSeriesId}});
                        } else if(rtPath) {
                            if (userId) {
                                window.location.replace(rtPath);
                            } else {
                                me.$Router.replace(rtPath);
                            }
                        } else {
                            if (userId) {
                                window.location.replace(rtPath);
                            } else {
                                me.$Router.replaceAll('/')
                            }
                        }
                    } else {
                        if (userId) {
                            window.location.replace(rtPath);
                        } else {
                            me.$Router.replaceAll('/');
                        }
                    }
                });
        },
        getData() {
            const productId = this.orderPayInfo.productId;
            const data = this.orderPayInfo.wxPayMpOrderResult;
            const rtPath = this.orderPayInfo.rtPath;
            const userId = this.orderPayInfo.userId;
            if (wechat && wechat.isWechat()) {
                console.log('wechat', data)
                //调用支付前应先处理订单信息，然后根据订单信息返回支付需要的timestamp,noncestr,package,signType,paySign等参数
                //下面的rs.data为后台处理完订单后返回的；我的业务模式为用户点击提交订单，请求后台添加订单接口，订单添加完成后，后台根据订单id，订单金额等信息调用微信签名拿到timestamp,noncestr等参数返回;
                wechat.wxPay({//调用支付，
                    timestamp: data.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
                    nonceStr: data.nonceStr, // 支付签名随机串，不长于 32 位
                    package: data.packageValue, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
                    signType: data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                    paySign: data.paySign, // 支付签名
                },function (res) {
                    console.log(res);
                    if(productId) {
                        this.goto('/game', {id: productId})
                    } else {
                        if (userId) {
                            window.location.replace(rtPath);
                        } else {
                            this.$Router.pushTab('/index')
                        }
                    }
                });
            }
        }
    }
}
