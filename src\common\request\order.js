import {get, post, postByService} from "@/common/request/jz-request";
import {config} from "@/common/jz-constant";

export const submitCoin = params => post('/order/' + config.appId + '/purchase/coin/submit', params)
export const submitCoinByUniPay = params => post('/order/' + config.appId + '/purchase/coin/submit1', params)
export const submitCoinBackup = params => post('/order/' + config.appId + '/purchase/backup/coin/submit1', params)
export const buyGame = params => post('/order/' + config.appId + '/purchase/submit', params)
export const buyGameRaw = params => post('/order/' + config.appId + '/purchase/raw/submit', params)
export const getOrderInfo = params => get('/order/info', params)
export const getWxJsapiSignature = params => get('/order/' + config.appId + '/jsapi/signature', params)
export const getBackupPayInfo = (orderId, originTenantId) => get('/order/backup/payInfo/' + orderId, {xAppId: config.appId, originTenantId: originTenantId})
export const checkBackupLicense = () => get('/order/backup/check/license')
