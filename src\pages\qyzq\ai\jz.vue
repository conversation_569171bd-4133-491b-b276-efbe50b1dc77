<template>
  <view class="content">
    <text class="noData">{{topTips}}</text>
    <scroll-view :scroll-into-view="scrollIntoView" scroll-y="true" class="msg-list" :enable-flex="true">
      <uni-ai-msg ref="msg" v-for="(msg,index) in msgList" :key="index" :msg="msg" @changeAnswer="changeAnswer"
                  :show-cursor="index === msgList.length - 1 && msgList.length%2 === 0 && sseIndex"
                  :isLastMsg="index === msgList.length - 1" @removeMsg="removeMsg(index)" @textClick="textClick(msg)"></uni-ai-msg>
<!--      <template v-if="msgList.length%2 !== 0">-->
<!--        <view v-if="requestState === -100" class="retries-box">-->
<!--          <text>消息发送失败</text>-->
<!--          &lt;!&ndash;          <uni-icons @click="send" color="#d22" type="refresh-filled" class="retries-icon"></uni-icons>&ndash;&gt;-->
<!--        </view>-->
<!--        <view class="tip-ai-ing" v-else-if="sseIndex>0">-->
<!--          <text>AI正在思考中...</text>-->
<!--        </view>-->
<!--      </template>-->
      <view id="last-msg-item" style="height: 1px;"></view>
    </scroll-view>
    <view class="foot-box" :style="{'padding-bottom':footBoxPaddingBottom}">
      <!-- #ifdef H5 -->
      <view class="pc-menu" v-if="isWidescreen">
        <view class="pc-trash pc-menu-item" @click="clearAllMsg" title="删除">
          <image src="@/common/static/remove.png" mode="heightFix"></image>
        </view>
      </view>
      <!-- #endif -->
      <view class="stop-box">
        <view @click="closeSseChannel" class="stop-responding" v-if="sseIndex"> ▣ 停止响应</view>
      </view>
      <charge ref="popup" title="余额不足，请及时充值"></charge>
      <view class="foot-box-content">
        <view v-if="!isWidescreen" class="menu">
          <uni-icons class="menu-item" @click="clearAllMsg" type="trash" size="24" color="#888"></uni-icons>
        </view>
        <view class="msg-tag-box">
          <view class="msg-tag" @click="sendGetSeriesMsg(question)" v-for="(question,index) in questionList" :key="index">{{question}}</view>
        </view>
      </view>
    </view>
    <global-web-socket ref="ws_client" :uri="wssUrl" @onMsg="onMsg" />
  </view>
</template>

<script>

import {getCustomers, getDictListByType, getMyFansToken} from "@/common/request/admin";
import store from "@/store";
import {isEmpty} from "@/util/validate";
import {guid, showMsg} from "@/common/jz-util";
import uniFab from '@/components/uni-fab'
import GlobalWebSocket from '@/components/websocket/GlobalWebSocket'
import UniAiMsg from "@/components/uni-ai-chat/uni-ai-msg.vue";
import SliceMsgToLastMsg from './SliceMsgToLastMsg.js';
import {formatDate} from "@/components/uni-dateformat/date-format";
import {getTodayAiGameList} from "@/common/request/promo-series";
import {getSeriesGame} from '@/common/request/promo-series'
import promoBuy from "@/pages/home/<USER>/promo-buy";
import charge from "@/pages/customer/components/charge";
import wechatPay from "@/pages/order/wechat-pay";
import website from "@/common/website";
import constantsAi from "@/pages/qyzq/ai/constants-ai";

export default {
  components: {GlobalWebSocket,UniAiMsg,charge},
  mixins: [promoBuy, constantsAi],
  computed: {
    // 输入框是否禁用
    inputBoxDisabled() {
      // 如果正在等待流式响应，则禁用输入框
      if (this.sseIndex !== 0) {
        return true
      }
      // 如果消息列表长度为奇数，则禁用输入框
      return this.sseQueue.length > 0 || this.sseContent.length > 0
    },
    // 获取当前环境
    NODE_ENV() {
      return process.env.NODE_ENV
    },
    footBoxPaddingBottom() {
      return (this.keyboardHeight || 2) + 'px'
    }
  },
  data() {
    return {
      wssUrl: "/" + website.wssService + "/ws/info",
      topTips: '',
      questionList: [],
      rawPay: true,
      // 使聊天窗口滚动到指定元素id的值
      scrollIntoView: "",
      // 消息列表数据
      msgList: [],
      // 通讯请求状态
      requestState:0,//0发送中 100发送成功 -100发送失败
      // 输入框的消息内容
      content: "",
      // 记录流式响应次数
      sseIndex: 0,
      // 是否启用流式响应模式
      enableStream: true,
      // 当前屏幕是否为宽屏
      isWidescreen: false,
      llmModel: false,
      keyboardHeight: 0,
      msgId: "",
      sseContent: [],
      sseQueue: [],
    };
  },
  onLoad(options) {
    this.getDict();
  },
  onReady() {
    this.$refs.ws_client.initWebSocket();
  },
  onHide() {
    this.$refs.ws_client.closeWebSocket();
  },
  mounted() {
  },
  methods: {
    getDict() {

      getDictListByType("ai_config").then((res) => {
        const dict = res.data;
        console.log(dict)
        for (let item of dict) {
          if (item.label === 'jz_model_question_list') {
            this.questionList = item.value.split(',');
          } else if (item.label === 'jz_model_top_tips') {
            this.topTips = item.value;
          }
        }
      }).catch(res => {});
    },
    buildMsg(isAi, messageId, action, content, ex, isEnd) {
      return { isAi: isAi, messageId: messageId, action: action, content: content, type: "HY", finishReason: isEnd ? "stop": "", ex: ex, create_time: formatDate(new Date()), isDelete: false };
    },
    addAiMsg(messageId, action, content, ex, isEnd) {
      this.addMsg(true, messageId, action, content, ex, isEnd);
    },
    addRawMsg(msg) {
      this.msgList.push(msg);
      this.showLastMsg()
    },
    addMsg(isAi, messageId, action, content, ex, isEnd) {
      const item = this.buildMsg(isAi, messageId, action, content, ex, isEnd);
      this.showSliceMsg(item);
      // this.msgList.push(item);
      // this.showLastMsg()
    },
    sendGetSeriesMsg(question) {
      const msg = this.buildMsg(false, guid(), this.HyActionEnum.HY_MSG, question, null, true);
      this.beforeSend(msg, this.getSeries);
    },
    getSeries() {
      uni.showLoading();
      const queryParams = {
        aiFlag: true,
        // orderColumn: "hot",
      }
      getSeriesGame(queryParams).then(res=> {
        this.loadFlag = false;
        let emptyList = true;
        for (let i = 0; i < res.data.length; i++) {
          const data = res.data[i];
          if (data && data.hasPromotion) {
            let content = data.name;  //+ "<p>" + data.home + " VS " + data.away + "</p>"
            this.addAiMsg(data.id, this.HyActionEnum.JZ_GET_GAMES, content, { seriesId: data.id }, true)
            emptyList = false;
          }
        }
        if (emptyList) {
          this.addAiMsg(guid(), this.HyActionEnum.HY_MSG, "Ai正在努力中，请稍后再试", null, true)
        }
        uni.hideLoading();
      }).catch(res=>{
        this.loadFlag = false;
        uni.hideLoading();
      });
    },
    sendMsg(msg) {
      if (isEmpty(msg.content)) {
        return;
      }
      const wsClient = this.$refs.ws_client;
      wsClient.send(JSON.stringify(msg))
    },
    writeTimeoutContent() {
      const msg = this.msgList[this.msgList.length - 1];
      if (this.sseContent.length > 0) {
        const str = this.sseContent[0];
        msg.content += str;
        this.sseContent = this.sseContent.slice(1);
        this.sseIndex += 1;
        setTimeout(this.writeTimeoutContent, 70);
        this.showLastMsg();
      } else if (this.sseIndex > 0) {
        if (msg.finishReason === "stop") {
          console.log('结束所有', this.sseQueue.length)
          this.sseIndex = 0;
          this.showLastMsg();
          if (this.sseQueue.length > 0) {
            const sseMsg = this.sseQueue[0];
            this.showSliceMsg(sseMsg);
            this.sseQueue = this.sseQueue.slice(1);
          } else if (this.sseContent.length === 0) {
            this.closeSseChannel()
          }
        }
      }
    },
    showSliceMsg(msg) {
      // console.log("sseIndex", this.sseIndex, this.msgId, msg.messageId);
      if (this.sseIndex === 0) {
        this.msgId = msg.messageId;
        const msgContent = msg.content;
        let wordsArray;
        if (msgContent.indexOf('<') > -1) {
          wordsArray = this.splitHtmlAndTextOrdered(msgContent);
          console.log("msgArr", wordsArray)
        } else {
          wordsArray = msgContent.split('');
        }
        this.sseContent = this.sseContent.concat(wordsArray)
        msg.content = "";
        this.msgList.push(msg);
        this.sseIndex += 1;
        this.showSliceMsg(msg);
      } else if (this.msgId === msg.messageId) {
        if (this.sseIndex === 1) {
          this.writeTimeoutContent();
        }
      } else {
        this.sseQueue.push(msg);
      }
    },
    splitHtmlAndTextOrdered(content) {
      // 匹配HTML标签和文本内容
      const regex = /(<[^>]*>)|([^<>]+)/g;

      // 使用match()方法获取所有匹配项
      const matches = content.match(regex) || [];

      // 将相邻的HTML标签拼接在一起，并将匹配项添加到一个数组中
      const result = matches.reduce((acc, match) => {
        if (/^<[^>]*>$/.test(match)) {
          // 如果是HTML标签，将其添加到上一个项中（如果上一个项是HTML标签）
          if (acc.length > 0 && /^<[^>]*>$/.test(acc[acc.length - 1])) {
            acc[acc.length - 1] += match;
          } else {
            acc.push(match);
          }
        } else {
          // 否则，将其添加到一个新的数组项中
          acc = acc.concat(match.split(''));
        }
        return acc;
      }, []);

      return result;
    },
    onMsg(msgObj) {
      let game;
      switch (msgObj.action) {
        case this.HyActionEnum.HY_MSG:
          msgObj.isAi = true;
          msgObj.content = msgObj.content || msgObj.error;
          this.addAiMsg(msgObj.messageId, msgObj.action, msgObj.content, msgObj.ex, msgObj.finishReason === "stop");
          break;
        case this.HyActionEnum.JZ_BUY_GAME:
          // 返回待购买的赛事，所以action是购买
          msgObj.isAi = true;
          game = msgObj.ex;
          console.log("返回消息", msgObj)
          this.addAiMsg(msgObj.messageId, msgObj.action, msgObj.content, msgObj.ex, msgObj.finishReason === "stop");
          break;
        case this.HyActionEnum.JZ_GET_GAME:
          msgObj.isAi = true;
          game = msgObj.ex;
          if (game && game.buyStatus === 1) {
            msgObj.content = "以下是 " + game.gameName + "的详细内容：<p>" + game.home + " VS " + game.away + "</p>";
            msgObj.content += game.gameRemark;
            msgObj.content += "<p>以上内容，仅由AI生成，请勿用于违法行为</p>";
          } else {
            msgObj.content = msgObj.error;
          }
          console.log("返回消息", msgObj)
          this.addAiMsg(msgObj.messageId, msgObj.action, msgObj.content, msgObj.ex, msgObj.finishReason === "stop");
          break;
      }
    },
    // 换一个答案
    async changeAnswer() {
      // 如果问题还在回答中需要先关闭
      if (this.sseIndex) {
        this.closeSseChannel()
      }
      //删除旧的回答
      this.msgList.pop()
      this.updateLastMsg({
        // 防止 偶发答案涉及敏感，重复回答时。提问内容 被卡掉无法重新问
        illegal: false
      })
      this.sendMsg()
    },
    async beforeSend(msg, successCallback) {
      console.log('push msgList')
      if (this.inputBoxDisabled) {
        return uni.showToast({
          title: 'ai正在回复中不能发送',
          icon: 'none'
        });
      }
      // 如果内容为空
      if (!msg.content) {
        // 弹出提示框
        return uni.showToast({
          // 提示内容
          title: '内容不能为空',
          // 不显示图标
          icon: 'none'
        });
      }

      // 将用户输入的消息添加到消息列表中
      this.addRawMsg(msg);
      if (successCallback) {
        successCallback();
      } else {
        this.sendMsg(msg) // 发送消息
      }
    },
    // 滚动窗口以显示最新的一条消息
    showLastMsg() {
      // 等待DOM更新
      this.$nextTick(() => {
        // 将scrollIntoView属性设置为"last-msg-item"，以便滚动窗口到最后一条消息
        this.scrollIntoView = "last-msg-item"
        // 等待DOM更新，即：滚动完成
        this.$nextTick(() => {
          // 将scrollIntoView属性设置为空，以便下次设置滚动条位置可被监听
          this.scrollIntoView = ""
        })
      })
    },
    // 更新最后一条消息
    updateLastMsg(param) {
      let length = this.msgList.length
      if (length === 0) {
        return
      }
      let lastMsg = this.msgList[length - 1]

      // 如果param是函数，则将最后一条消息作为参数传入该函数
      if (typeof param == 'function') {
        let callback = param;
        callback(lastMsg)
      } else {
        // 否则，将参数解构为data和cover两个变量
        const [data, cover = false] = arguments
        if (cover) {
          lastMsg = data
        } else {
          lastMsg = Object.assign(lastMsg, data)
        }
      }
      this.msgList.splice(length - 1, 1, lastMsg)
    },
    removeMsg(index) {
      // 成对删除，如果点中的是 ai 回答的内容，index -= 1
      if (this.msgList[index].isAi) {
        index -= 1
      }

      // 如果删除的就是正在问的，且问题还在回答中需要先关闭
      if (this.sseIndex && index === this.msgList.length - 2) {
        this.closeSseChannel()
      }

      this.msgList.splice(index,2)
    },
    textClick(msg) {
      // const gameId = msg.messageId;
      if (this.inputBoxDisabled) {
        return uni.showToast({
          title: 'ai正在回复中不能发送',
          icon: 'none'
        });
      }
      if (msg.isAi === true) {
        switch (msg.action) {
          case this.HyActionEnum.JZ_GET_GAMES:
            const seriesId = msg.ex.seriesId;
            const content = msg.content;
            msg = this.buildMsg(false, msg.messageId, msg.action, content, seriesId, true);
            this.beforeSend(msg)
            break;
          case this.HyActionEnum.JZ_BUY_GAME:
            this.onByGame(msg);
            break;
          default:
            break;
        }
      }
    },
    onByGame(msg) {
      const game = msg.ex
      if (game.buyStatus !== 1) {
        this.buy(game, '单次', null, this.onPaySuccess);
      }
    },
    onPaySuccess(game) {
      console.log('onPaySuccess', game)
      const content = "我已购买赛事：" + game.gameName + "，请告诉我详细的内容。";
      const msg = this.buildMsg(false, guid(), this.HyActionEnum.JZ_GET_GAME, content, game.id, true);
      setTimeout(()=> {this.beforeSend(msg)}, 1500);
      // const wsClient = this.$refs.ws_client;
      // const msgObj = {
      //   type: "JZ",
      //   action: 0,
      //   content: game.id
      // };
      // wsClient.send(JSON.stringify(msgObj))
      // this.getTodayAiGameList()
    },
    closeSseChannel() {
      // // 如果存在消息通道，就关闭消息通道
      // if (sseChannel) {
      //   sseChannel.close()
      //   // 设置为 false 防止重复调用closeSseChannel时出错
      //   sseChannel = false
      //   this.sliceMsgToLastMsg.end()
      // }
      // // 清空历史网络请求（调用云对象）任务
      // // uniCoTaskList.clear()
      // // 将流式响应计数值归零
      this.sseContent = [];
      this.sseQueue = [];
      this.sseIndex = 0;
    },
    // 清空消息列表
    clearAllMsg(e) {
      // 弹出确认清空聊天记录的提示框
      uni.showModal({
        title: "确认要清空聊天记录？",
        content: '本操作不可撤销',
        complete: (e) => {
          // 如果用户确认清空聊天记录
          if (e.confirm) {
            // 关闭ssh请求
            this.closeSseChannel()
            // 将消息列表清空
            this.msgList.splice(0, this.msgList.length);
          }
        }
      });
    }

  }
};
</script>

<style lang="scss">
@import "@/pages/qyzq/ai/hy.scss";
page,
.content {
  height: 100%;
  background-color: $page-color-base;
  display: flex;
  flex-direction: column;
}

.content {
  padding: 0;
  .noData {
    margin: 0;
    padding: 40rpx 8rpx;
    color: #ee1b1b;
  }
  .button-box {
    padding: 40rpx 80rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .jz-button {
      margin: 40rpx 0;
      height: 80rpx;
      line-height: 80rpx;
      font-size: $font-lg;
    }
  }
}

.foot-box-content {
  padding: 0;
  .menu {
    align-items: center;
  }
  .msg-tag-box {
    flex: 1;
    height: 100rpx;
    padding: 0 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .msg-tag {
      font-size: $font-lg;
      margin-left: 16rpx;
      display: inline-block;
      align-items: center;
      justify-content: center;
      background: #4276b60f;
      color: #000;
      padding: 0 16rpx;
      height: 52rpx;
      line-height: 52rpx;
      border-radius: 16rpx;
    }
  }
}

</style>