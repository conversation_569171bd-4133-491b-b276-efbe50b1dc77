<template>
    <view class="series">
        <view class="series-top">
            <view class="series-img" align="center">
                <image :src="series.image|productImg" mode="widthFix"/>
            </view>
            <view class="series-content-box">
                <view class="series-memo-box">
                    <view class="top-title">
                        {{series.name}}
                    </view>
                    <view class="bottom-tag">
                        <text class="game-tag m-r">近{{winHistory.times}}中{{winHistory.winTimes}}</text>
                        <text class="game-tag m-r" v-if="series.weekRate">周胜率 {{series.weekRate}}%</text>
                    </view>
                </view>
                <view class="series-add" v-if="series.followId!==undefined">
                    <view v-if="!series.followId" class="jz-button full-border" @click.stop="followGame(promoSeriesId)">
                        <text class="ylh-icon icon-jia"></text>
                        <text class="jz-button-text">关注</text>
                    </view>
                    <view v-else class="jz-button followed" @click.stop="unFollowGame(promoSeriesId)">
                        <text class="ylh-icon icon-gouxuan"></text>
                        <text class="jz-button-text">已关注</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="series-bottom">
            <view class="series-memo" v-if="series.promotion">
                <!--                        <text class="text-tag">介绍</text>-->
                <text class="series-memo-content">
                    {{series.promotion}}
                </text>
            </view>
            <com-promo-win-times :results="results"></com-promo-win-times>
        </view>
    </view>
</template>

<script>
import {
    followGame,
    unFollowGame,
    getGameResultText,
    getSeriesId,
    getGameItemBeforePlayTime
} from '@/common/request/promo-series'
import ComPromoWinTimes from "@/pages/qyzq/components/com-promo-win-times";

export default {
    name: "ComPromoSeriesHead",
    components: {ComPromoWinTimes},
    props: {
        promoSeriesId: {
            type: String,
            default: function(){
                return undefined;
            }
        },
        game: {
          type: Object,
          default: function(){
            return undefined;
          }
        }
    },
    filters: {},
    data() {
        return {
            results: [],
            series: {
                followId: undefined
            },
            winHistory: {
                times: 10,
                winTimes: 0,
            },
        }
    },
    mounted() {
        if(this.promoSeriesId) {
            this.getSeriesById();
            this.getResults();
        }
    },
    methods: {
        getSeriesById() {
            const me = this;
            getSeriesId(me.promoSeriesId).then(res=> {
                me.series = res.data;
            }).catch(res=>{});
        },
        getResults() {
            const me = this;
            getGameResultText(me.promoSeriesId).then(res=> {
                if(res.data) {
                    me.results = res.data.split('');
                    let times = 0;
                    me.winHistory.times = me.results.length;
                    for(let i = 0; i < me.results.length; i++) {
                        if(this.results[i]==='W') {
                            times++;
                            me.winHistory.winTimes++;
                        }
                    }
                    if(times > 0) {
                        me.winTimes = times+'连胜';
                    }
                }
            }).catch(res=>{});
        },
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
        followGame(promoSeriesId) {
            const me = this;
            followGame({promoSeriesId: promoSeriesId}).then(res=>{
                me.series.followId = '1';
            }).catch(res=>{});
        },
        unFollowGame(promoSeriesId) {
            const me = this;
            unFollowGame(promoSeriesId).then(res=>{
                me.series.followId = 0;
            }).catch(res=>{});
        },
    }
}
</script>

<style lang="scss">
.series {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 10rpx 8rpx 12rpx 14rpx;
    .series-top {
        display: flex;
        flex-direction: row;
        .series-img {
            width: 120rpx;
            image {
                width: 120rpx;
                height: 120rpx;
            }
        }
        .series-content-box {
            display: flex;
            flex-direction: row;
            flex: 1;
            .series-memo-box {
                flex: 1;
                display: flex;
                flex-direction: column;
                .top-title {
                    flex: 1;
                    font-size: $font-lg + 8rpx;
                    font-weight: 700;
                    padding: 0 8rpx;
                }
                .bottom-tag {
                    flex: 1;
                    padding: 0 8rpx;
                    font-size: $font-base;
                    display: flex;
                    align-items: center;
                    flex-direction: row;
                    .game-tag {
                        display: inline-block;
                        align-items: center;
                        justify-content: center;
                        background: #ff0000;
                        color: #ffffff;
                        padding: 4rpx 22rpx;
                        border-radius: 8rpx;
                    }
                }
            }
            .series-add {
                width: 148rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-right: 8rpx;
                .jz-button {
                    font-size: $font-base;
                    padding: 4rpx;
                    background-color: $primary-color-base;
                    color: $primary-color-white;
                    .ylh-icon {
                        font-size: $font-base;
                    }
                    .jz-button-text {
                        padding-left: 4rpx;
                    }
                }
                .full-border {
                    border: 4rpx solid $primary-color-base;
                }
                .followed {
                    border: 2rpx solid $primary-color-base;
                    background: transparent;
                    color: $primary-color-base;
                }
            }
        }
    }
    .series-bottom {
        padding: 24rpx 10rpx 12rpx 10rpx;

        .series-memo {
            border-radius: 12rpx;
            background-color: #f7f7f7;
            padding: 8rpx 48rpx 16rpx 36rpx;
            //box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
            display: flex;
            flex-direction: row;
            .text-tag {
                width: 80rpx;
            }
            .series-memo-content {
                padding-left: 8rpx;
            }
        }
    }


}
</style>
