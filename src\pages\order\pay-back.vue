<template>
  <view class="order-info">
    <view class="game-info" v-if="!isEmpty(game)">
      <view class="game-title">
        <text>{{game.gameName}}</text>
      </view>
      <view class="game-race">
        <text>{{game.home}}</text>
        <text class="game-race-vs">VS</text>
        <text>{{game.away}}</text>
      </view>
      <view class="game-remark" v-if="game.gameRemark">
        <text>内容：</text>
        <rich-text :nodes="game.gameRemark"></rich-text>
      </view>
    </view>
    <view class="charge-box" v-if="isBuyGame">
      <view class="jz-button-background charge-button" @click.stop="goUrl()">
        <view class="jz-button1 gradient full-border">
<!--          <text class="ylh-icon icon-fanhui"></text>-->
          <text class="jz-button-text">跳转到赛事</text>
        </view>
      </view>
    </view>
    <jz-empty v-if="isEmpty(game)" content="获取数据中.."></jz-empty>
  </view>
</template>

<script>
import {config} from "@/common/jz-constant";
import {getGamePromoItemAfterPay} from "@/common/request/user-game";
import jzEmpty from '@/components/jz-empty';

export default {
  name: "PayBack",
  components: {jzEmpty},
  data() {
    return {
      /** 商户订单号 */
      out_trade_no: "",
      queryJson: "",
      check_code: "",
      isBuyGame: false,
      game: {}
    };
  },
  mounted() {
    let mchData = {
      action: "onIframeReady",
      displayStyle: "SHOW_CUSTOM_PAGE"
    };
    let postData = JSON.stringify(mchData);
    parent.postMessage(postData, "https://payapp.weixin.qq.com");
    if (document.querySelector("html").style.fontSize == "0px")
      document.querySelector("html").style.fontSize = "36px";
  },
  created() {
    let query = this.$route.query;
    console.log(query)
    this.queryJson = JSON.stringify(query);
    this.out_trade_no = query.out_trade_no;
    this.check_code = query.check_code;
    setTimeout(()=>{ this.getGameInfo() }, 1500);
  },
  methods: {
    goUrl() {
      const id = this.game.id;
      const promoSeriesId = this.game.promoSeriesId;
      const url = config.restUrl + "/" + config.appId + "/game?id=" + id + "&promoSeriesId=" + promoSeriesId;
      /** jumpOutUrl 跳转的页面 */
      const mchData = {
        action: "jumpOut",
        jumpOutUrl: url
      };
      const pData = JSON.stringify(mchData);
      parent.postMessage(pData, "https://payapp.weixin.qq.com");
    },
    getGameInfo() {
      getGamePromoItemAfterPay(this.out_trade_no, this.check_code).then(res => {
        this.game = res.data;
        if (this.game != null) {
          this.isBuyGame = true;
        }
      }).catch(res => {
        this.msg = res;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.order-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .game-info {
    padding: 20rpx 0;
    text-align: center;
    font-size: $font-base;
    .game-title {
      font-size: $font-lg;
      padding: 20rpx 10rpx;
    }
    .game-race {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .game-race-vs {
        padding: 0 12rpx;
      }
    }
    .game-remark {
      padding: 20rpx 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }
  .jz-button-background {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .charge-box {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    margin-top: 34rpx;
    padding: 20rpx 100rpx;

    .charge-button {
      .jz-button1 {
        color: #ffffff;
        width: 100%;
        font-size: $font-base;
        border-radius: 8rpx;
        text-align: center;
        padding: 16rpx;

        .ylh-icon {
          font-size: $font-lg + 18rpx;
        }
      }
      .jz-button-text {
        font-size: $font-lg + 12rpx;
      }
      .gradient {
        /* 设置容器尺寸 - 原理1 */
        //width: 400px;
        //height: 400px;
        /* 背景渐变色 - 原理2 */
        background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
        /* 背景尺寸 - 原理3 */
        background-size: 200% 200%;
        /* 循环动画 - 原理4 */
        animation: gradientBG 5s ease infinite;
      }
      /* 动画，控制背景 background-position */
      @keyframes gradientBG {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
    }
  }
}
</style>