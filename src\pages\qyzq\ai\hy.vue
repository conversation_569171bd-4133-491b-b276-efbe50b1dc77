<template>
  <view class="content">
    <text class="noData" v-if="msgList.length === 0">没有对话记录</text>
    <scroll-view :scroll-into-view="scrollIntoView" scroll-y="true" class="msg-list" :enable-flex="true">
      <uni-ai-msg ref="msg" v-for="(msg,index) in msgList" :key="index" :msg="msg" @changeAnswer="changeAnswer"
                  :show-cursor="index === msgList.length - 1 && msgList.length%2 === 0 && sseIndex"
                  :isLastMsg="index === msgList.length - 1" @removeMsg="removeMsg(index)"></uni-ai-msg>
<!--      <template v-if="msgList.length%2 !== 0">-->
<!--        <view v-if="requestState === -100" class="retries-box">-->
<!--          <text>消息发送失败</text>-->
<!--&lt;!&ndash;          <uni-icons @click="send" color="#d22" type="refresh-filled" class="retries-icon"></uni-icons>&ndash;&gt;-->
<!--        </view>-->
<!--        <view class="tip-ai-ing" v-else-if="sseIndex>0">-->
<!--          <text>ai正在思考中...</text>-->
<!--        </view>-->
<!--      </template>-->
      <view id="last-msg-item" style="height: 1px;"></view>
    </scroll-view>
    <view class="foot-box" :style="{'padding-bottom':footBoxPaddingBottom}">
      <!-- #ifdef H5 -->
      <view class="pc-menu" v-if="isWidescreen">
        <view class="pc-trash pc-menu-item" @click="clearAllMsg" title="删除">
          <image src="@/common/static/remove.png" mode="heightFix"></image>
        </view>
      </view>
      <!-- #endif -->
      <view class="stop-box">
        <view @click="closeSseChannel" class="stop-responding" v-if="sseIndex"> ▣ 停止响应</view>
      </view>
      <view class="foot-box-content">
        <view v-if="!isWidescreen" class="menu">
          <uni-icons class="menu-item" @click="clearAllMsg" type="trash" size="24" color="#888"></uni-icons>
        </view>
        <view class="textarea-box">
					<textarea v-model="content" :cursor-spacing="15" class="textarea" :auto-height="!isWidescreen"
                    placeholder="请输入要发给ai的内容" :maxlength="-1" :adjust-position="false"
                    :disable-default-padding="false" placeholder-class="input-placeholder"></textarea>
        </view>
        <view class="send-btn-box" :title="(msgList.length && msgList.length%2 !== 0) ? 'ai正在回复中不能发送':''">
          <!-- #ifdef H5 -->
          <text v-if="isWidescreen" class="send-btn-tip">↵ 发送 / shift + ↵ 换行</text>
          <!-- #endif -->
          <button @click="beforeSend" :disabled="inputBoxDisabled || !content" class="send"
                  type="primary">发送</button>
        </view>
      </view>
    </view>
    <global-web-socket ref="ws_client" :uri="wssUrl" @onMsg="onMsg" />
  </view>
</template>

<script>

import {getCustomers, getMyFansToken} from "@/common/request/admin";
import store from "@/store";
import {isEmpty} from "@/util/validate";
import {showMsg} from "@/common/jz-util";
import uniFab from '@/components/uni-fab'
import GlobalWebSocket from '@/components/websocket/GlobalWebSocket'
import UniAiMsg from "@/components/uni-ai-chat/uni-ai-msg.vue";
import SliceMsgToLastMsg from './SliceMsgToLastMsg.js';
import {formatDate} from "@/components/uni-dateformat/date-format";
import website from "@/common/website";

export default {
  components: {GlobalWebSocket,UniAiMsg},
  computed: {
    // 输入框是否禁用
    inputBoxDisabled() {
      // 如果正在等待流式响应，则禁用输入框
      if (this.sseIndex !== 0) {
        return true
      }
      // 如果消息列表长度为奇数，则禁用输入框
      return false
    },
    // 获取当前环境
    NODE_ENV() {
      return process.env.NODE_ENV
    },
    footBoxPaddingBottom() {
      return (this.keyboardHeight || 2) + 'px'
    }
  },
  data() {
    return {
      wssUrl: "/" + website.wssService + "/ws/info",
      // 使聊天窗口滚动到指定元素id的值
      scrollIntoView: "",
      // 消息列表数据
      msgList: [],
      thoughtList: [],
      // 通讯请求状态
      requestState:0,//0发送中 100发送成功 -100发送失败
      // 输入框的消息内容
      content: "",
      // 记录流式响应次数
      sseIndex: 0,
      // 是否启用流式响应模式
      enableStream: true,
      // 当前屏幕是否为宽屏
      isWidescreen: false,
      llmModel: false,
      keyboardHeight: 0,
      msgId: ""
    };
  },
  onReady() {
    this.$refs.ws_client.initWebSocket();
  },
  onHide() {
    this.$refs.ws_client.closeWebSocket();
  },
  onLoad(options) {
  },
  mounted() {
    // this.sendMsg()
  },
  methods: {
    // sendMsg() {
    //   const wsClient = this.$refs.ws_client;
    //   const timer = setTimeout(() => {
    //     const is_connected = wsClient.getState()
    //     console.log("is_connected", is_connected)
    //     if (is_connected) {
    //
    //       clearTimeout(timer)
    //     }
    //   }, 1000);
    // },
    sendMsg() {
      if (isEmpty(this.content)) {
        return;
      }
      const wsClient = this.$refs.ws_client;
      const msgObj = {
        type: "HY",
        action: 0,
        content: this.content
      };
      console.log("发送数据", this.content)
      wsClient.send(JSON.stringify(msgObj))
    },
    onMsg(msgObj) {
      if (msgObj.type === "HY" && (msgObj.action === 0 || msgObj.action === 4)) {
        msgObj.isAi = true;
        msgObj.isDelete = false
        msgObj.create_time = formatDate(new Date())
        const list = this.msgList;
        msgObj.isThought = msgObj.action !== 0;
        if (this.msgId === "" || this.msgId !== msgObj.msgId) {
          this.msgId = msgObj.msgId
          list.push(msgObj)
          this.sseIndex = 1;
        } else {
          const lastMsg = list[list.length - 1];
          lastMsg.content += msgObj.content;
          if (msgObj.finishReason === "stop") {
            this.sseIndex = 0;
            this.msgId = "";
            console.log('结束 sseIndex:', this.sseIndex)
          } else {
            this.sseIndex += 1;
          }
        }
        this.showLastMsg()
      }
    },
    // 换一个答案
    async changeAnswer() {
      // 如果问题还在回答中需要先关闭
      if (this.sseIndex) {
        this.closeSseChannel()
      }
      //删除旧的回答
      this.msgList.pop()
      this.updateLastMsg({
        // 防止 偶发答案涉及敏感，重复回答时。提问内容 被卡掉无法重新问
        illegal: false
      })
      this.sendMsg()
    },
    async beforeSend() {
      if (this.inputBoxDisabled) {
        return uni.showToast({
          title: 'ai正在回复中不能发送',
          icon: 'none'
        });
      }
      // 如果内容为空
      if (!this.content) {
        // 弹出提示框
        return uni.showToast({
          // 提示内容
          title: '内容不能为空',
          // 不显示图标
          icon: 'none'
        });
      }

      // 将用户输入的消息添加到消息列表中
      this.msgList.push({isAi: false, create_time: formatDate(new Date()), isDelete: false, content: this.content})

      // 展示最后一条消息
      this.showLastMsg()
      // dom加载完成后 清空文本内容
      this.$nextTick(() => {
        this.content = ''
      })
      this.sendMsg() // 发送消息
    },
    // 滚动窗口以显示最新的一条消息
    showLastMsg() {
      // 等待DOM更新
      this.$nextTick(() => {
        // 将scrollIntoView属性设置为"last-msg-item"，以便滚动窗口到最后一条消息
        this.scrollIntoView = "last-msg-item"
        // 等待DOM更新，即：滚动完成
        this.$nextTick(() => {
          // 将scrollIntoView属性设置为空，以便下次设置滚动条位置可被监听
          this.scrollIntoView = ""
        })
      })
    },
    // 更新最后一条消息
    updateLastMsg(param) {
      let length = this.msgList.length
      if (length === 0) {
        return
      }
      let lastMsg = this.msgList[length - 1]

      // 如果param是函数，则将最后一条消息作为参数传入该函数
      if (typeof param == 'function') {
        let callback = param;
        callback(lastMsg)
      } else {
        // 否则，将参数解构为data和cover两个变量
        const [data, cover = false] = arguments
        if (cover) {
          lastMsg = data
        } else {
          lastMsg = Object.assign(lastMsg, data)
        }
      }
      this.msgList.splice(length - 1, 1, lastMsg)
    },
    removeMsg(index) {
      // 成对删除，如果点中的是 ai 回答的内容，index -= 1
      if (this.msgList[index].isAi) {
        index -= 1
      }

      // 如果删除的就是正在问的，且问题还在回答中需要先关闭
      if (this.sseIndex && index === this.msgList.length - 2) {
        this.closeSseChannel()
      }

      this.msgList.splice(index,2)
    },
    closeSseChannel() {
      // // 如果存在消息通道，就关闭消息通道
      // if (sseChannel) {
      //   sseChannel.close()
      //   // 设置为 false 防止重复调用closeSseChannel时出错
      //   sseChannel = false
      //   this.sliceMsgToLastMsg.end()
      // }
      // // 清空历史网络请求（调用云对象）任务
      // // uniCoTaskList.clear()
      // // 将流式响应计数值归零
      this.sseIndex = 0
    },
    // 清空消息列表
    clearAllMsg(e) {
      // 弹出确认清空聊天记录的提示框
      uni.showModal({
        title: "确认要清空聊天记录？",
        content: '本操作不可撤销',
        complete: (e) => {
          // 如果用户确认清空聊天记录
          if (e.confirm) {
            // 关闭ssh请求
            this.closeSseChannel()
            // 将消息列表清空
            this.msgList.splice(0, this.msgList.length);
          }
        }
      });
    }

  }
};
</script>

<style lang="scss">
@import "@/pages/qyzq/ai/hy.scss";
page,
.content {
  height: 100%;
  background-color: $page-color-base;
  display: flex;
  flex-direction: column;
}

.content {
  padding-top: 20rpx;
  .button-box {
    padding: 40rpx 80rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .jz-button {
      margin: 40rpx 0;
      height: 80rpx;
      line-height: 80rpx;
      font-size: $font-lg;
    }
  }
}

</style>