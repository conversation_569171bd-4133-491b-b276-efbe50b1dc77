<template>
    <uni-card isShadow>
        <template v-slot:header>
            <view class="com-card-title">
                <view class="title-left">
                    <view class="game-name">
                      <text class="game-name-text">{{item.seriesName.replace('（点击获取）','') + ' - ' + item.gameName.replace('（点击获取）','')}}</text>
                      <text class="play-time">{{item.playTime | date('MM-dd hh:mm')}}</text>
                    </view>
                </view>
                <view class="game-right" v-if="!isEmpty(item.result)">
                  <view class="game-result-icon" :class="resultClass(item.result)">{{item.result|gameResult}}</view>
                </view>
            </view>
        </template>
        <view class="com-card-content">
            <view class="jz-row" v-if="item.combineGames">
                <view class="game-home-away combine-box" v-for="(game, index) in getCombineGames(item.combineGames)" :key="index">
                    <text class="game-play-time">{{game.playTime}}</text>
                    <text class="game-home deg-box">{{game.home}}</text>
                    <text class="game-vs">VS</text>
                    <text class="game-away deg-box">{{game.away}}</text>
                </view>
            </view>
            <view class="jz-row" v-else>
                <view class="game-home-away">
                    <text class="game-home deg-box">{{item.home}}</text>
                    <text class="game-vs">VS</text>
                    <text class="game-away deg-box">{{item.away}}</text>
                </view>
            </view>
            <view class="series-memo m-t" v-if="item.gameRemark">
              <view class="series-memo-content">
                <text>推介方案：</text>
                <rich-text :nodes="item.gameRemark"></rich-text>
              </view>
              <view class="series-memo-content" v-if="item.score">
                <text>比分：</text>
                <text>{{item.score}}</text>
              </view>
            </view>
        </view>
        <template v-slot:footer>
        </template>
    </uni-card>
</template>

<script>
import UniGrid from '@/components/uni-grid/uni-grid.vue'
import UniGridItem from '@/components/uni-grid/uni-grid-item.vue'
import UniCard from '@/components/uni-card.vue'
import uniLoadMore from '@/components/uni-load-more.vue'
import jzEmpty from '@/components/jz-empty.vue';
import {buyGame} from '@/common/request/order'
import {isEmpty} from "@/util/validate";

export default {
    name: "comPromoAi",
    components: {UniGrid,UniGridItem,UniCard,uniLoadMore,jzEmpty},
    props: {
        item: {
            type: Object,
            default: function(){
                return {};
            }
        },
        dateFlag: false
    },
    methods: {
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
        getCombineGames(games) {
            const gameArray = games.split('\n');
            let combineGamesList = [];
            for(let game of gameArray) {
                const gameContentArray = game.split(",");
                if(gameContentArray.length === 3) {
                    combineGamesList.push({playTime: gameContentArray[0], home:gameContentArray[1], away: gameContentArray[2]});
                }
            }
            return combineGamesList;
        }
    }
}
</script>

<style lang="scss">
.com-card-title {
    flex: 1;
    display: flex;
    justify-content: space-between;
    .title-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .game-name{
        display: flex;
        flex: auto;
        align-items: center;
        padding-left: 12rpx;
        .game-name-text {
            flex: 1;
            font-size: $uni-font-size-lg;
            color: #000;
            font-weight: 500;
        }
    }
    .game-image {
        width: 68rpx;
        height: 68rpx;
        image {
            border-radius:50%;
            width: 100%;
            height: 100%;
        }
    }
    .play-time {
        width: 160rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        text-align: right;
        color: $font-color-light;
        font-size: $font-sm;
    }
}
.com-card-content {
    padding: 12rpx 12rpx 12rpx 24rpx;
    .jz-row {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    .game-home-away {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 24rpx 48rpx 10rpx 36rpx;
        .game-play-time {
            font-size: $font-sm;
            padding-right: 12rpx;
        }
        .deg-box {
            position: relative;
            padding: 4rpx 0;
            font-size: $font-sm;
        }
        .deg-box::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: -1;
            transform: skew(-8deg);
        }
        .game-home {
            flex: 1;
            color: #fff;
        }
        .game-home::before {
            background: #0542b1;
        }
        .game-vs {
            width: 100rpx;
        }
        .game-away {
            flex: 1;
            color: #fff;
        }
        .game-away::before {
            background: #ff0000;
        }
    }
    .combine-box {
        padding: 24rpx 24rpx 10rpx 12rpx;
        .game-vs {
            width: 80rpx;
        }
    }
    .card-content-box {
        display: flex;
        flex-direction: row;
    }
    .game-left {
        flex: auto;
        display: flex;
        flex-direction: column;
        color: $font-color-base;
        font-size: $font-lg;
        .game-title {
            font-weight: bold;
        }
        .game-title-time {
            padding-bottom: 6rpx;
            font-weight: 400;
            color: #000;
            font-size: $font-sm;
        }
        .game-home-away {
            height: 50%;
            line-height: 82rpx;
            color: $font-color-disabled;
        }
    }
}

.game-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 120rpx;
  padding-left: 20rpx;
  padding-right: 10rpx;
}
.game-result-icon {
  display:flex;
  justify-content:center;
  border-radius:50%;
  width:60rpx;
  height:60rpx;
  line-height:54rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-right: 20rpx;
}
.color-red {
  color:red;
  border:2rpx solid red;
}
.color-black {
  color:#000;
  border:2rpx solid #000;
}
.color-green {
  color: #4cb84b;
  border:2rpx solid #4cb84b;
}

.com-card-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .com-card-bottom-left {
        .promo-date {
            //color: $uni-text-color-grey;
            //font-size: $uni-font-size-sm;
        }
    }
    .com-card-bottom-right {
        .com-button {

        }
    }
}
.jz-button {
    font-size: $font-lg - 4rpx;
    padding: 12rpx 12rpx;
    background-color: $primary-color-light;
}
.disabled {
  background-color: $font-color-disabled;
}
.game-price {
  font-size: $font-base;
}
.game-price-padding {
  padding-left: 24rpx;
}
.font-red {
  color:red;
}
</style>
