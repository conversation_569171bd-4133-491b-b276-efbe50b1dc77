<template>
    <view class="game-win-box" v-if="!isEmpty(results)">
        <text>近十场成绩：</text>
        <text :class="resultClass(itemResult)" v-for="(itemResult, index) of results" :key="index">
            {{itemResult}}
        </text>
    </view>

</template>

<script>

export default {
    name: "comPromoWinTimes",
    props: {
        results: {
            type: Array,
            default: function(){
                return [];
            }
        }
    },
    methods: {
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.game-win-box {
    .color-red {
        color: #e7234e;
    }
    .color-black {
        color: #4a4c55;
    }
    .color-green {
        color: #4cb84b;
    }
}
</style>