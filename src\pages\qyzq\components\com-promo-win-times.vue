<template>
    <view class="game-win-box" v-if="!isEmpty(results)">
        <text>近十场成绩：</text>
        <text class="game-result-icon" :class="resultClass(itemResult)" v-for="(itemResult, index) of results" :key="index">
            {{itemResult|gameResult}}
        </text>
    </view>

</template>

<script>

export default {
    name: "comPromoWinTimes",
    props: {
        results: {
            type: Array,
            default: function(){
                return [];
            }
        }
    },
    methods: {
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.game-win-box {
    padding: 14rpx 8rpx 6rpx 8rpx;
    .game-result-icon {
        display: inline-block;
        justify-content:center;
        align-items: center;
        text-align: center;
        border-radius:50%;
        width:38rpx;
        height:38rpx;
        line-height: 34rpx;
        font-size: 24rpx;
        font-weight: 600;
        margin-right: 8rpx;
    }

    .color-red {
        color:red;
        border:1px solid red;
    }
    .color-black {
        color:#000;
        border:1px solid #000;
    }
    .color-green {
        color: #4cb84b;
        border:1px solid #4cb84b;
    }
}
</style>