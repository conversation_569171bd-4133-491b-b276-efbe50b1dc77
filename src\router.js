// router.js
import {RouterMount,createRouter} from 'uni-simple-router';
import store from '@/store'
import {config} from "@/common/jz-constant";
import getters from "@/store/getters";
import wechat from 'pages/order/wechat.js';

const router = createRouter({
    debug: true,
    platform: process.env.VUE_APP_PLATFORM,
    routes: [...ROUTES]
});
//全局路由前置守卫
router.beforeEach((to, from, next) => {
    console.log('from', from);
    console.log('to', to);
    store.dispatch('user/isLogin', to).then(()=> {
        console.log('isLogin true')
        next();
    }).catch(()=> {
        console.log('isH5', config.isH5)
        if (config.isH5) {
            if (to.name === 'login') {
                console.log('开始进入登录页面')
                next();
            } else {
                store.dispatch('user/storeNextRoute', to).then(()=> {
                    next({name:'login'});
                });
            }
        }
        else {
            let params = getUrlParams();
            console.log('isLogin false', params)
            if(params.code) {
                console.log('微信回调进入,使用路由传参');
                store.dispatch('user/login', params.code).then((res)=> {
                    const nextRoute = store.getters.nextRoute;
                    console.log('登陆成功，开始跳转', nextRoute)
                    const authRouter = { path: nextRoute.aliasPath || nextRoute.path, query: nextRoute.query, NAVTYPE: 'replace'};
                    console.log('authRouter', authRouter)
                    store.dispatch('user/storeNextRoute', null).then(()=> {
                        next(authRouter);
                    })
                }).catch((res)=>{
                    next({name:'login'});
                });
            } else {
                console.log('取不到url参数', to);
                store.dispatch('user/storeNextRoute', to).then(()=> {
                    store.dispatch('user/getWechatLoginUrl').then((res)=> {
                        console.log('getWechatLoginUrl', res.data);
                        window.location.href = res.data;
                    }).catch((res)=>{});
                });

                // //url没有code：1、外部进入，2、内部跳登录
                // if(to.name==="login") {
                //     console.log('内部跳转');
                //     //内部跳转登录，告诉auth需要从后台触发微信回调
                //     next();
                // } else {
                //     console.log('其他链接');
                //     //外部进入，需先跳转登录
                //     //next({name:'login'});
                // }
            }
        }
    })

});
// 全局路由后置守卫
router.afterEach((to, from) => {
    console.log('跳转结束1')
})

const getUrlParams = () => {
    //debugger;
    let url = window.location.href;
    console.log('util-getUrlParams-window.location.href:'+url);
    let urlArrX = url.split('#');
    console.log('pre #:'+urlArrX[0]);
    let urlArr = urlArrX[0].split('?');
    console.log('pre ?:' + urlArr[0]);
    let params={};
    if(urlArr.length > 1){
        let cs = urlArr[1];
        let cs_arr = cs.split('&');
        for(let i=0;i<cs_arr.length;i++){
            params[cs_arr[i].split('=')[0]] = cs_arr[i].split('=')[1];
            //console.log(cs_arr[i].split('=')[1]);
        }
    }

    return params;
}

export {
    router,
    RouterMount
}