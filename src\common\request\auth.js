import { config } from '../jz-constant'
import website from "@/common/website";
import {authRequest, delByService, getByService, globalRequest, postByService} from "@/common/request/jz-request";
import * as CryptoJS from 'crypto-js'

export const auth = {
  logout: () => delByService('auth', '/token/logout'),

  wechat: {
    getUrl: () => getByService('admin', '/login/' + config.appId + '/url/' + website.path, null),
    loginByCode: params => postByService('auth', '/mobile/token/social?grant_type=mobil&mobile=WX@' + params.code + '@' + config.appId, null),
    loginByOpenId: params => authRequest('auth', '/mobile/token/social?grant_type=mobil&mobile=SUPERJSTAR@' + params.openId + '@' + config.appId+ params.managerOpenId, 'POST', null),
    loginByUserName: params => postByService('auth', '/wechatMini/public/username/login', params),
    decryptionPhone: params => postByService('auth', '/wechatMini/decryptionPhone', params),
    logoutDelBinding: () => postByService('auth', '/wechatMini/logoutDelBinding'),
  },
  aliPay: {

  },
  h5: {
    getCode: (data) => globalRequest('', 'code', 'GET', data, null),
    codeCheck: (params) => globalRequest('',  'code/check','post', null, params),
    loginByPassword: (code, data, randomStr) => globalRequest('auth', '/oauth/token?randomStr=' + randomStr + '&grant_type=password&code=' + encodeURIComponent(code), 'POST', data, null,  { 'content-type': 'application/x-www-form-urlencoded' }),
  }
}

export const encryption = (params) => {
  let {
    data,
    type,
    param,
    key
  } = params
  const result = JSON.parse(JSON.stringify(data))
  if (type === 'Base64') {
    param.forEach(ele => {
      result[ele] = btoa(result[ele])
    })
  } else {
    param.forEach(ele => {
      let data = result[ele]
      key = CryptoJS.enc.Latin1.parse(key)
      let iv = key
      // 加密
      let encrypted = CryptoJS.AES.encrypt(
          data,
          key, {
            iv: iv,
            mode: CryptoJS.mode.CFB,
            padding: CryptoJS.pad.NoPadding
          })
      result[ele] = encrypted.toString()
    })
  }
  return result
}