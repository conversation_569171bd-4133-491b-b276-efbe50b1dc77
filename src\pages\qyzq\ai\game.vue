<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y  @scrolltolower="getHistoryGameList(true)">

            <view class="m-t" v-if="historyPromoList && historyPromoList.length > 0">
                <uni-section title="历史成绩" type="line"></uni-section>
                <view class="promo-list">
                    <com-promo-ai :dateFlag="true" :item="itemK" v-for="(itemK, index) in historyPromoList" :key="index"></com-promo-ai>
                </view>
                <uni-load-more v-if="pageInfo.size > 0 && historyPromoList.length > 0" :status="loadStatus"></uni-load-more>
            </view>
            <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
        </scroll-view>
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromoAi from '../components/com-promo-ai'
import uniFab from '@/components/uni-fab'
import uniLoadMore from '@/components/uni-load-more'
import uniCountdown from '@/components/uni-countdown'
import {getHistoryGameList} from "@/common/request/promo-series";
import {isEmpty} from "@/util/validate";
import ComPromoWinTimes from "@/pages/qyzq/components/com-promo-win-times";

export default {
    components: {ComPromoWinTimes, uniSection, uniCard, comPromoAi, uniFab, uniLoadMore,uniCountdown},
    data() {
        return {
            rawPay: true,
            results: [],
            gameId: '',
            game: {
                gameRemark: 'x'
            },
            expired: {
                d: undefined,
                h: undefined,
                m: undefined,
                s: undefined
            },
            winHistory: {
                times: 10,
                winTimes: 0,
            },
            historyPromoList: [],
            loadStatus: 'more',
            loadFlag: false,
            pageInfo: {
                current: 0,
                size: 8,
                aiFlag: true,
            },

        }
    },
    onLoad() {
    },
    onShow() {
      this.getHistoryGameList();
    },
    methods: {
        getHistoryGameList(nextPage) {
            if(this.loadFlag){
                return;
            }
            this.loadFlag = true;
            if(nextPage) {
                this.pageInfo.current += 1;
            } else {
                this.loadStatus = 'more';
                this.historyPromoList = [];
                this.pageInfo.current = 1;
            }
            if(this.loadStatus !== 'more') {
                this.loadFlag = false;
                return;
            }
            uni.showLoading();
            getHistoryGameList(this.pageInfo).then(res=> {
                this.loadFlag = false;
                const data = res.data.records;
                if(!isEmpty(data)) {
                    this.historyPromoList = this.historyPromoList.concat(data);
                }
                if(!data || data.length < this.pageInfo.size) {
                    this.loadStatus = 'noMore'
                }
                uni.hideLoading();
            }).catch(res=>{
                this.loadFlag = false;
                uni.hideLoading()
            });
        },
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
        calcTime(d) {
            const now = new Date().getTime();
            const end = new Date(d).getTime();
            const leftTime = end - now;
            if (leftTime >= 0) {
                this.expired.d = Math.floor(leftTime / 1000 / 60 / 60 / 24);
                this.expired.h = Math.floor(leftTime / 1000 / 60 / 60 % 24);
                this.expired.m = Math.floor(leftTime / 1000 / 60 % 60);
                this.expired.s = Math.floor(leftTime / 1000 % 60);
            }
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
}
.bg-white {
    background-color: #FFFFFF;
}
.jz-row {
    width: 100%;
}
.game-box {
    padding-top: 16rpx;
    .game-box-content {
        //border-radius: 12rpx;
        //background-color: #cccccc;
        padding: 8rpx 48rpx 16rpx 36rpx;
        //box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
    }
    .game-play-time {
        font-size: $font-base;
        color: #888888;
        padding: 8rpx 4rpx 16rpx 4rpx;
    }
    .game-home-away {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        .deg-box {
            position: relative;
            padding: 4rpx 0;
            font-size: $font-base + 2rpx;
            z-index: 1;
        }
        .deg-box::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: -1;
            transform: skew(-12deg);
        }
        .game-home {
            flex: 1;
            color: #fff;
        }
        .game-home::before {
            background: #0542b1;
        }
        .game-vs {
            width: 100rpx;
        }
        .game-away {
            flex: 1;
            color: #fff;
        }
        .game-away::before {
            background: #ff0000;
        }
    }
    .game-score {
        font-size: $font-sm;
        color: #4a4c55;
        border-radius: 12rpx;
        //background-color: #cccccc;
        padding: 8rpx 48rpx 8rpx 36rpx;
        box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
    }
}
.series {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 10rpx 8rpx 12rpx 14rpx;
    .series-top {
        display: flex;
        flex-direction: row;
        .series-img {
            width: 120rpx;
            image {
                width: 120rpx;
                height: 120rpx;
            }
        }
        .series-content-box {
            display: flex;
            flex-direction: row;
            flex: 1;
            .series-memo-box {
                flex: 1;
                display: flex;
                flex-direction: column;
                .top-title {
                    flex: 1;
                    font-size: $font-lg + 8rpx;
                    font-weight: 700;
                    padding: 0 8rpx;
                }
                .bottom-tag {
                    flex: 1;
                    padding: 0 8rpx;
                    font-size: $font-base;
                    display: flex;
                    align-items: center;
                    flex-direction: row;
                    .game-tag {
                        display: inline-block;
                        align-items: center;
                        justify-content: center;
                        background: #ff0000;
                        color: #ffffff;
                        padding: 4rpx 22rpx;
                        border-radius: 8rpx;
                    }
                }
            }
            .series-add {
                width: 148rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-right: 8rpx;
                .jz-button {
                    font-size: $font-base;
                    padding: 4rpx;
                    background-color: $primary-color-base;
                    color: $primary-color-white;
                    .ylh-icon {
                        font-size: $font-base;
                    }
                    .jz-button-text {
                        padding-left: 4rpx;
                    }
                }
                .full-border {
                    border: 4rpx solid $primary-color-base;
                }
                .followed {
                    border: 2rpx solid $primary-color-base;
                    background: transparent;
                    color: $primary-color-base;
                }
            }
        }
    }
    .series-bottom {
        padding: 24rpx 10rpx 12rpx 10rpx;

        .series-memo {
            border-radius: 12rpx;
            background-color: #f7f7f7;
            padding: 8rpx 48rpx 16rpx 36rpx;
            //box-shadow: 0 0 4rpx rgba(0, 0, 0, .3);
            display: flex;
            flex-direction: row;
            .text-tag {
                width: 80rpx;
            }
            .series-memo-content {
                padding-left: 8rpx;
            }
        }
    }


}
.buy-box {
    display: flex;
    flex-direction: row;
    color: $primary-color-white;
    padding: 12rpx 16rpx;
    .ylh-icon {
        font-size: 36rpx;
    }
    .buy-text {
        padding-left: 4rpx;
    }
}



.text-tag {
    display: inline-block;
    align-items: center;
    justify-content: center;
    background: $primary-color-base;
    color: #ffffff;
    padding: 0 8rpx;
    font-size: $font-sm;
}
.discount-buy-box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20rpx 60rpx;
    align-items: center;
    .jz-button {
        width: 40%;
    }
    .jz-button-week {
        background-color: #e7234e;
        padding: 16rpx 8rpx;
    }
    .jz-button-month {
        background-color: #F79A07;
        padding: 16rpx 8rpx;
    }
}
.promo-reason {
    background-color: #fff;
    .promo-content {
        padding: 10rpx 24rpx;
        font-size: $font-lg;
        background-color: #fff;
        text-indent: 2em;
        color: $font-color-light;
    }
    .promo-content-combine {
        padding: 24rpx;
        line-height: 48rpx;
        background: transparent;
        display: flex;
        .promo-remark {
            flex: 1;
        }
        .game-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 120rpx;
            padding-left: 20rpx;
            padding-right: 10rpx;
            .game-result-icon {
                display:flex;
                justify-content:center;
                border-radius:50%;
                width:60rpx;
                height:60rpx;
                line-height:54rpx;
                font-size: 32rpx;
                font-weight: 600;
                margin-right: 20rpx;
            }
            .color-red {
                color:red;
                border:2rpx solid red;
            }
            .color-black {
                color:#000;
                border:2rpx solid #000;
            }
            .color-green {
                color: #4cb84b;
                border:2rpx solid #4cb84b;
            }
        }
    }
}
.promo-sec-bg {
    width: 100%;
    display: flex;
    //background-position: center bottom;
    //background-size: cover;
    background-repeat: no-repeat;
    background-image: url($media-url + '/admin/sys-file/fb-mp/promo_sec_bg.png');
    background-size:100% 100%;
    .promo-sec {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 120rpx 0;
        .jz-button {
            background: transparent;
            border: none;
            color: $primary-color-base;
            .ylh-icon {
                font-size: 62rpx;
            }
        }
        .jz-button-text {
            font-weight: 700;
            font-size: $font-lg + 6rpx;
            padding-left: 4rpx;
        }
    }
}
.mention-box {
    background-color: #fff;
    padding-bottom: 40rpx;
    .mention {
        padding: 24rpx;
        color: $font-color-disabled;
        font-size: $font-sm;
    }
}
</style>
