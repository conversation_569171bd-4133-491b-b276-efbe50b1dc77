<template>
  <view class="content">
    <view class="button-box">
      <view class="jz-button" @click.stop="goto('/ai/hy')">
        {{freeModelName}}
      </view>
      <view class="jz-button" @click.stop="goto('/ai/jz')">
        {{jzModelName}}
      </view>
      <view class="jz-button" @click.stop="goto('/ai/game')">
        {{historyName}}
      </view>
    </view>
  </view>
</template>

<script>

import {getDictListByType} from "@/common/request/admin";

export default {
  components: {},
  data() {
    return {
      freeModelName: '',
      jzModelName: '',
      historyName: '',
    };
  },
  mounted() {
    this.getDict()
  },
  onLoad(options) {
  },
  methods: {
    getDict() {
      getDictListByType("ai_config").then((res) => {
        const dict = res.data;
        for (let item of dict) {
          if (item.label === 'free_model_name') {
            this.freeModelName = item.value;
          } else if (item.label === 'jz_model_name') {
            this.jzModelName = item.value;
          } else if (item.label === 'history_name') {
            this.historyName = item.value;
          }
        }
      }).catch(res => {});
    }
  }
};
</script>

<style lang="scss">
page,
.content {
  height: 100%;
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  /* 背景尺寸 - 原理3 */
  background-size: 200% 200%;
  /* 循环动画 - 原理4 */
  animation: gradientBG 2s ease infinite;
}

.content {
  padding: $page-row-spacing $page-row-spacing 0 $page-row-spacing;

  .button-box {
    padding: 40rpx 80rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .jz-button {
      margin: 40rpx 0;
      height: 80rpx;
      line-height: 80rpx;
      font-size: $font-lg;
      background: white;
      color: $font-color-light;
      border: 2rpx solid $border-color-base;
    }
  }
}

</style>