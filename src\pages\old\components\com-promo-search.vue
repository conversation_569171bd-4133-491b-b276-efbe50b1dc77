<template>
    <view class="promo-search-box">
        <uni-grid :column="3" :highlight="false" :show-border="false" @change="change">
            <uni-grid-item v-for="(item, index) in groups" :index="index" :key="index">
                <view class="series-item empty-border" :class="{'full-border': index === selectedIndex}">
                    <view class="ylh-icon" :class="item.icon"></view>
                    <view class="text">{{ item.text }}</view>
                </view>
            </uni-grid-item>
        </uni-grid>
        <jz-confirm-modal v-if="confirmObj.settings.display" :settings.sync="confirmObj.settings" :inputs.sync="confirmObj.inputs"
                          :values.sync="confirmObj.values" @submit="confirmChange"
                          @close="confirmObj.settings.display=false"></jz-confirm-modal>
    </view>

</template>

<script>
import UniGrid from '@/components/uni-grid/uni-grid'
import UniGridItem from '@/components/uni-grid/uni-grid-item'
import website from "@/common/website";
import JzConfirmModal from "@/components/jz-confirm-modal";

export default {
    name: "comPromoSearch",
    components: {UniGrid,UniGridItem,JzConfirmModal},
    props: {
    },
    data() {
        return {
            groups: website.comPromoSearch.groups,
            selectedIndex: -1,
            //默认，只用于绑定
            confirmObj: {
                settings: {
                    display: false,		//是否显示
                    title: "",	//标题
                    buttonText: '提交'	//确认按钮文本
                },
                inputs: [{
                    vModel: 'orderColumn',
                }],
                values: {
                    orderColumn: ''
                }
            }
        }
    },
    methods: {
        change(e) {
            let { index } = e.detail
            this.selectedIndex = index;
            let item = this.groups[this.selectedIndex];
            const { confirmObj, orderColumn, orderAsc} = item;
            if(confirmObj) {
                this.confirmObj = confirmObj;
                this.confirmObj.settings.display = true;
            } else {
                let item = this.groups[this.selectedIndex];
                if(this.selectedIndex === 4) {
                    let text = item.text;
                    switch (text) {
                        default:
                        case "价格排序": item.text = "价格降序"; item.orderAsc = false; break;
                        case "价格升序": item.text = "价格降序"; item.orderAsc = false; break;
                        case "价格降序": item.text = "价格升序"; item.orderAsc = true; break;
                    }
                }
                else {
                    this.groups[4].text = '价格排序';
                }
                const { orderColumn, orderAsc} = item;
                this.$emit('search', { orderColumn, orderAsc });
            }

        },
        confirmChange(data) {
            this.confirmObj.settings.display = false;
            if (data) {
                const { orderColumn, orderAsc} = data;
                this.$emit('search', { orderColumn, orderAsc });
            }

            // let { index } = e.detail
            // this.selectedIndex = index;
            // let item = this.groups[this.selectedIndex];

        },
    }
}
</script>

<style lang="scss">
.promo-search-box {
    padding: 10rpx;
    background-color: $primary-color-base;
    border-radius: 2px;
}
.empty-border {
  border: 1px solid $primary-color-base;
}
.series-item {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 8rpx;
    .ylh-icon {
        font-size: 40rpx;
        color: $primary-color-dark;
    }
    .text {
        padding-left: 4rpx;
        font-size: $font-search;
        font-weight: 700;
        color: $primary-color-white;
    }
}
.full-border {
  border-color: $border-color-dark;
  border-radius: 8rpx;
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.1);
}
.order-cnt {
    position: absolute;
    top: 10upx;
    right: 30upx;
}
</style>