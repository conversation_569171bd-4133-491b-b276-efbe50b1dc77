<template>
  <view class="content">
    <view class="form-container">
      <view class="input-row form-title">
        <text>{{ alreadyHadPhone ? changePassword ? '更改密码' : '您已绑定以下手机号' : '绑定手机号' }}</text>
      </view>
      <uni-forms ref="formChangePwd" v-show="changePassword" :modelValue="changePwdForm" :labelWidth="100">
        <uni-forms-item required label="旧密码：" name="oldPassword" labelAlign="right">
          <uni-easyinput type="password" v-model="changePwdForm.oldPassword" placeholder="请输入您的旧密码" />
        </uni-forms-item>
        <uni-forms-item required name="password" label="设置新密码：" labelAlign="right">
          <uni-easyinput type="password" v-model="changePwdForm.password" placeholder="请输入新密码" />
        </uni-forms-item>
        <uni-forms-item required name="confirmPassword" label="确认新密码：" labelAlign="right">
          <uni-easyinput type="password" v-model="changePwdForm.confirmPassword" placeholder="请确认新密码" />
        </uni-forms-item>
      </uni-forms>
      <uni-forms ref="formChangePhone" v-show="!changePassword" :modelValue="changePhoneForm" :labelWidth="100">
        <uni-forms-item v-if="!changePassword" required label="手机号：" name="phoneNumber" labelAlign="right">
          <uni-easyinput type="text" v-model="changePhoneForm.phoneNumber" placeholder="请输入您的手机号" />
        </uni-forms-item>
        <uni-forms-item v-if="!alreadyHadPhone" required name="password" label="设置密码：" labelAlign="right">
          <uni-easyinput type="password" v-model="changePhoneForm.password" placeholder="请输入密码" />
        </uni-forms-item>
        <uni-forms-item v-if="!alreadyHadPhone" required name="confirmPassword" label="确认密码：" labelAlign="right">
          <uni-easyinput type="password" v-model="changePhoneForm.confirmPassword" placeholder="请输入确认密码" />
        </uni-forms-item>
      </uni-forms>
    </view>
    <view class="btn-row">
      <button v-if="!alreadyHadPhone" type="primary" class="primary" :loading="loginBtnLoading" @tap="submit">提交</button>
      <button v-if="changePassword" type="primary" class="primary" :loading="loginBtnLoading" @tap="submitChangePassword">修改密码</button>
      <block v-else>
        <view class="memo" v-if="alreadyHadPhone && !changePassword">
          <text class="jz-alink" @click="showChangePwd">修改密码</text>
        </view>
        <view class="memo">
          如果您需要改绑手机号，请<text class="jz-alink" @click="gotoName('contact')">联系客服</text>。
        </view>
      </block>
    </view>
    <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
  </view>
</template>

<script>
import uniFab from '@/components/uni-fab'
import uniForms from '@/components/uni-forms/uni-forms.vue'
import uniFormsItem from '@/components/uni-forms-item/uni-forms-item.vue'
import uniEasyinput from '@/components/uni-easyinput/uni-easyinput.vue'
import {updatePhone, updatePassword} from "@/common/request/customer";
import {showMsg} from "@/common/jz-util";
import {isEmpty} from "@/util/validate";
import {mapState} from "vuex";

export default {
  components: {uniFab,uniForms,uniFormsItem,uniEasyinput},
  computed: {
    ...mapState({
      userInfo: state => {
        return state.user.userInfo
      }
    }),
    alreadyHadPhone() {
      return this.userInfo && this.userInfo.phone;
    }
  },
  data() {
    return {
      changePassword: false,
      changePhoneForm: {
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      },
      changePwdForm: {
        oldPassword: "",
        password: "",
        confirmPassword: "",
      },
      changePhoneFormRules: {
        // 对name字段进行必填验证
        phoneNumber: {
          rules: [{
            required: true,
            errorMessage: '手机号不能为空',
          }, {
            errorMessage: '手机号只能是数字',
            format: 'number',
          }, {
            errorMessage: '请输入正确的手机号',
            pattern: "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$"
          }]
        },
        password: {
          rules: [{
            required: true,
            errorMessage: '请输入密码',
          }, {
              minLength: 8,
              maxLength: 24,
              errorMessage: '密码长度在 {minLength} 到 {maxLength} 个字符',
          }]
        },
        confirmPassword: {
          rules: [{
            required: true,
            errorMessage: '请输入确认密码',
          }, {
            minLength: 8,
            maxLength: 24,
            errorMessage: '密码长度在 {minLength} 到 {maxLength} 个字符',
          }, {
            validateFunction: (rule, value, data, callback) => {
              // 异步需要返回 Promise 对象
              return new Promise((resolve, reject) => {
                if (value === this.changePhoneForm.password) {
                  resolve();
                } else {
                  reject('两次密码必须一样');
                }
              })
            }
          }]
        }
      },
      changePwdFormRules: {
        oldPassword: {
          rules: [{
            required: true,
            errorMessage: '请输入旧密码',
          }, {
            minLength: 8,
            maxLength: 24,
            errorMessage: '密码长度在 {minLength} 到 {maxLength} 个字符',
          }]
        },
        password: {
          rules: [{
            required: true,
            errorMessage: '请输入密码',
          }, {
            minLength: 8,
            maxLength: 24,
            errorMessage: '密码长度在 {minLength} 到 {maxLength} 个字符',
          }]
        },
        confirmPassword: {
          rules: [{
            required: true,
            errorMessage: '请输入确认密码',
          }, {
            minLength: 8,
            maxLength: 24,
            errorMessage: '密码长度在 {minLength} 到 {maxLength} 个字符',
          }, {
            validateFunction: (rule, value, data, callback) => {
              // 异步需要返回 Promise 对象
              return new Promise((resolve, reject) => {
                if (value === this.changePwdForm.password) {
                  resolve();
                } else {
                  reject('两次密码必须一样');
                }
              })
            }
          }]
        }
      },
      loginBtnLoading: false,
    }
  },
  onReady() {
    // 需要在onReady中设置规则
    this.$refs.formChangePhone.setRules(this.changePhoneFormRules);
    this.$refs.formChangePwd.setRules(this.changePwdFormRules);
  },
  onShow() {
    //this.getData();
    if (this.userInfo.phone) {
      this.changePhoneForm.phoneNumber = this.userInfo.phone;
    }
  },
  methods: {
    getData() {
    },
    showChangePwd() {
      this.changePassword = true;
    },
    submit() {
      this.$refs.formChangePhone.validate().then(res=>{
        updatePhone(res).then(res => {
          showMsg("绑定成功，正在重新登录");
          setTimeout(()=>{
            this.$store.dispatch("user/logout").then(()=> {
              this.gotoName('index');
            });
          },1500);
        }).catch(res => {
          console.log('updatePhone', res);
        });
        // console.log('表单数据信息：', res);
      }).catch(err =>{
        // console.log('表单错误信息：', err);
      })
    },
    submitChangePassword() {
      this.$refs.formChangePwd.validate().then(res=>{
        updatePassword(res).then(res => {
          showMsg("修改成功，正在重新登录");
          setTimeout(()=>{
            this.$store.dispatch("user/logout").then(()=> {
              this.gotoName('index');
            });
          },1500);
        }).catch(res => {
          console.log('updatePhone', res);
        });
        // console.log('表单数据信息：', res);
      }).catch(err =>{
        // console.log('表单错误信息：', err);
      })
    }
  }
}
</script>

<style lang="scss">
.content {
  width: 100%;
  .form-container {
    background-color: #FFFFFF;
    padding: 48rpx 38rpx 0 24rpx;

    .form-title {
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: $font-lg;
      font-weight: 700;
    }
    .uni-forms {
      padding-top: 24rpx;
      .uni-forms-item .uni-forms-item__label .label-text {
        font-size: $font-lg;
      }
    }

  }
  .btn-row {
    margin-top: 25px;
    padding: 10px;
    .memo {
      text-align: center;
      padding-top: 20rpx;
      color: $font-color-disabled;
      .jz-alink {
        padding: 0 8rpx;
      }
    }
  }
  .input-group {
    background-color: #ffffff;
    margin-top: 20px;
    position: relative;
  }
  .input-group::before {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #c8c7cc;
  }
  .input-group::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #c8c7cc;
  }
  .input-row {
    display: flex;
    flex-direction: row;
    position: relative;
    /* font-size: 18px; */
    height: 40px;
    line-height: 40px;
  }
  .input-row .title {
    width: 70px;
    padding-left: 15px;
  }
  .input-row.border::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 8px;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #c8c7cc;
  }
}
.login-type {
  display: flex;
  justify-content: center;
}

.login-type-btn {
  line-height: 30px;
  margin: 0px 15px;
}

.login-type-btn.act {
  color: #0FAEFF;
  border-bottom: solid 1px #0FAEFF;
}

.send-code-btn {
  width: 120px;
  text-align: center;
  background-color: #0FAEFF;
  color: #FFFFFF;
}

.action-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.action-row navigator {
  color: #007aff;
  padding: 0 10px;
}

.oauth-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.oauth-image {
  position: relative;
  width: 50px;
  height: 50px;
  border: 1px solid #dddddd;
  border-radius: 50px;
  background-color: #ffffff;
}

.oauth-image image {
  width: 30px;
  height: 30px;
  margin: 10px;
}

.oauth-image button {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.captcha-view {
  line-height: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
  background-color: #f3f3f3;
}
</style>
