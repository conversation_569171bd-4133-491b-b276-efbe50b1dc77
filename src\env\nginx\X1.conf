	server {
        listen       443 http2 ssl;
        server_name  x1.qyqy268.com;
        ssl_certificate      qyqy268.com.cer;
        ssl_certificate_key  qyqy268.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;
		
		gzip on;
		gzip_static on;
		gzip_min_length 1k;
		gzip_comp_level 4;
		gzip_proxied any;
		gzip_types text/plain text/xml text/css application/javascript;
		gzip_vary on;
		gzip_disable "MSIE [1-6]\.(?!.*SV1)";
		
		#root /home/<USER>/X/jstar-ui/;
		#root /home/<USER>/ui/front/win08/;
		
		
		
		# 避免端点安全问题
		if ($request_uri ~ "/actuator"){
			return 403;
		}
		
		location /wxd19c0f89a350beab {
			add_header Cache-Control "max-age=0";
			rewrite ^(.*)$  https://$host/wechat$1 permanent;
		}
		
		location ^~/h5/ {
			root /home/<USER>/X/;
			index  index.html index.htm;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header  Host $http_host;
            proxy_read_timeout 300;
			try_files /h5/wxd19c0f89a350beab/$uri $uri /h5/wxd19c0f89a350beab/index.html;
        }
		
		location /wxd19c0f89a350beab/order/WXPAY_verify_1626941218.txt {
			default_type text/plain;
			return 200 "e256b64705ecd62efe489330f01c8dcd";
		}
		
		location /wxd19c0f89a350beab/MP_verify_BSECoggydC0S4bWz.txt {
			default_type text/plain;
			return 200 "BSECoggydC0S4bWz";
		}
		
		# location ^~/wechat/ {
			# root /home/<USER>/X/;
			# index  index.html index.htm;
            # proxy_set_header  X-Real-IP  $remote_addr;
            # proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header  Host $http_host;
            # proxy_read_timeout 300;
			# try_files /wechat/wxd19c0f89a350beab/$uri $uri /wechat/wxd19c0f89a350beab/index.html;
        # }
	
		location ~* ^/football-g/ws/ {
            proxy_pass http://stream_x;
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_connect_timeout 120s;
			proxy_send_timeout 120s;
			proxy_read_timeout 3600s;
			proxy_http_version 1.1;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection "upgrade";
		}
		
        location ~* ^/(football|football-g|football-test|code|auth|admin|daemon|tx|act|monitor|mp|job|pay) {
			#proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://**********:9999;
        }
	
		location ~* ^/wechat/ {
			proxy_set_header Host $http_host;
			proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_connect_timeout 120s;
			proxy_send_timeout 120s;
			proxy_read_timeout 120s;
			proxy_pass http://**********:8089;
			#try_files $uri $uri/ /wechat/wx7125bd357001d576/index.html;
		}
		
		#location ~* /mgr/\.(gif|jpg|png|css|js|ttf|woff) {
		#	root /home/<USER>/X/;
		#	proxy_read_timeout 300;
		#	#try_files /mgr/$uri $uri 404;
		#}
		
		location / {
			proxy_set_header Host $http_host;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_read_timeout 3600s;
			proxy_connect_timeout 120s;
			proxy_send_timeout 120s;
			proxy_pass http://**********:8080;
        }

        #error_page   500 502 503 504  /50x.html;
        #location = /50x.html {
        #    root   html;
        #}
    }
