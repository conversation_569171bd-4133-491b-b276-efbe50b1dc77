<template>
    <view class="content">

        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniFab from '@/components/uni-fab';
import wechatPay from "@/pages/order/wechat-pay";
import {config} from "@/common/jz-constant";

export default {
    components: {uniFab},
    mixins: [wechatPay],
    onLoad() {
        this.orderPayInfo = this.getParams();
        if (this.orderPayInfo.tenantId) {
          config.tenantId = this.orderPayInfo.tenantId
        }
        this.returnPage = "";
        this.xxxPay();
    }
}
</script>

<style scoped>

</style>
