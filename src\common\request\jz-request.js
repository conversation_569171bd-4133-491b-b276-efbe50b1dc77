import {InfoLoginUser,config} from '@/common/jz-constant';
import store from '@/store';
import { isEmpty } from '@/util/validate'
import { showMsg, showMsgConfirmSingle, showMsgSingle } from '@/common/jz-util';

const urlConfig = config.restUrl;
const request = {}
let repeatCount = 0;

const getMsgByCode = function (res) {
	if(!res) {
		return '请求异常,连接超时';
	}
	let errMsg = res.errMsg;
	const statusCode = res.statusCode;
	// console.log('getMsgByCode', res)
	if(statusCode === 401 || statusCode === 4001){
		//var params = {url:url, method:method, data:data};
		errMsg = '亲,会话过期,请登录后重新操作';
		//发起vuex store发送
		//store.dispatch('common/req401');
	}else if(statusCode === 0){
		errMsg = '网络连接错误';
	}else if(statusCode === 404){
		errMsg = '目标资源不存在';
	}else if(statusCode === 403){
		errMsg = '访问受限';
	}else if(statusCode >= 500 && statusCode < 600){
		errMsg = '系统升级中';
	}else if(statusCode === -300){
		errMsg = '请求异常,连接超时';
	}else if(statusCode === 400){
		if (res.data && res.data.success === false && res.data.msg) {
			errMsg = res.data.msg;
		} else {
			errMsg = '系统升级中';
		}
	}
	if(errMsg === 'request:error'){
		if (!isEmpty(res.data) && !isEmpty(res.data.errMsg)) {
			errMsg = res.data.exception;
		}
	} else {
		if (!isEmpty(res.data) && res.data.code === 1) {
			errMsg = res.data.msg
		}
	}
	return errMsg;
};

export const get = (url, data) => {
	return getByService(config.restPathPrefix, url, data);
}

export const getByService = (restPathPrefix, url, data, params) => {
	return globalRequest(restPathPrefix, url, 'GET', data, params);
}

export const post = (url, data) => {
	return postByService(config.restPathPrefix, url, data);
}

export const postByService = (restPathPrefix, url, data) => {
	return globalRequest(restPathPrefix, url, 'POST', data);
}

export const del = (url, data) => {
	return globalRequest(config.restPathPrefix, url, 'DELETE', data);
}

export const delByService = (restPathPrefix, url, data) => {
	return globalRequest(restPathPrefix, url, 'DELETE', data);
}

export const parseParams = (params) => {
	if (params) {
		let url = '?';
		for (let p in params) {
			const pV = params[p];
			if (pV) {
				url += p + '=' + encodeURIComponent(pV) + '&';
			}
		}
		return url.substring(0, url.length - 1);
	}
	return '';
}

export const globalRequest = (pathPrefix, url, method, data, params, headers) => {
	if (!headers) {
		headers = {};
	}
	//json
	headers['X-Requested-With'] = 'XMLHttpRequest';
	headers['TENANT-ID'] = config.tenantId;
	//获取token
	const token = store.getters.access_token;
	if (token) {
		headers['Authorization'] = 'Bearer ' + token;
	} else {
		headers['Authorization'] = 'Basic anN0YXI6anN0YXI=';
	}
	//外部接口url不进行拼接
	if(url.indexOf('http://') < 0 && url.indexOf('https://') < 0 ){
		url = urlConfig + '/' + pathPrefix + url;
	}
	url += parseParams(params);
	return baseRequest(url, method, data, headers);

}

export const authRequest = (pathPrefix, url, method, data, params, headers) => {
	if (!headers) {
		headers = {};
	}
	//json
	headers['X-Requested-With'] = 'XMLHttpRequest';
	headers['TENANT-ID'] = config.tenantId;
	headers['Authorization'] = 'Basic anN0YXI6anN0YXI=';
	//外部接口url不进行拼接
	if(url.indexOf('http://') < 0 && url.indexOf('https://') < 0 ){
		url = urlConfig + '/' + pathPrefix + url;
	}
	url += parseParams(params);
	return baseRequest(url, method, data, headers);

}

function baseRequest(url, method, data, headers) {
	return new Promise((resolve, reject) => {
		uni.request({
			url: url,
			method: method,
			data: data,
			dataType: 'json',
			header: headers
		}).then(res => {
			//request:fail timeout
			//console.log(res)
			for(let res1 of res){
				if(res1) {
					if(res1 && res1.statusCode >= 200 && res1.statusCode < 300) {
						let result = res1.data;	// res1.data.data为接口返回的消息
						if(url.indexOf(urlConfig) >= 0) {
							//内部接口(简赞)
							if ((result.success === false) || (result.code && result.code > 200)) {
								res1.errMsg = result.msg || res1.errMsg;
								res1.statusCode = result.code;
								reject(res1);
								throw res1;
							}
							else {
								// 接口200，但data里面success=false，弹出消息
								if (result.data && result.data.success === false && result.data.msg) {
									res1.errMsg = result.data.msg;
									res1.statusCode = 400;
									reject(res1);
									throw res1;
								} else {
									repeatCount = 0;
									resolve(result);
									//return result;
								}
							}
						}
						else {
							//外部接口(第三方接口)
							repeatCount = 0;
							resolve(result);
							//return result;
						}
					}
					else {
						//http code异常
						reject(res1);
						throw res1;
					}
				}
			}
		}).catch(res => {
			console.log('catch', res)
			const statusCode = res.statusCode;
			if(statusCode === 401) {
				store.dispatch('common/req401')
					.then(()=> {
						//限制401的重复请求次数
						repeatCount += 1;
						if(repeatCount > 3) {
							repeatCount = 0;
							reject({errMsg:'请求已超限制', statusCode: 501})
						}
						else {
							// return globalRequest(url, method, data)
						}
					}).catch(() => {reject(res)});
			}
			else {
				const errMsg = getMsgByCode(res);
				uni.hideLoading();
				if(!isEmpty(errMsg) && errMsg.length>24) {
					showMsgConfirmSingle(errMsg,"确认",null);
				} else{
					showMsg(errMsg);
				}
				return reject(res)
			}
		})
	});
}

export default request
