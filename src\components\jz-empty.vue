<template>
	<view class="empty-content">
		<image class="empty-content-image" :src="$website.ossDomain + '/admin/sys-file/fb-mp/empty.png'"
               mode="aspectFit"></image>
		<view class="memo">{{content}}</view>
	</view>
</template>

<script>
	export default {
		props: {
			content: {
				type: String,
				default: ''
			},
		}
	}
</script>

<style lang="scss">
.empty-content {
    height: 80%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;

	&-image {
		width: 200upx;
	}
	image {
		height: 200upx;
	}
	.memo {
		height: 100upx;
		line-height: 100upx;
		font-size: $font-base;
		color: $font-color-disabled;
	}
}
</style>
