    server {
        listen       443 ssl;
        server_name  test.yj-star.com;
        ssl_certificate      test.yj-star.com.pem;
        ssl_certificate_key  test.yj-star.com.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        #charset koi8-r;

        #access_log  logs/mastertest.log  main;



		#root /home/<USER>/;

        location / {
			proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
			#proxy_set_header Connection "Keep-Alive";
            proxy_set_header  Host $http_host;
            #proxy_redirect off;
            proxy_read_timeout 300;
            proxy_pass http://localhost:2080;
        }
        #error_page   *********** 504  /50x.html;
        #location = /50x.html {
        #    root   html;
        #}
    }
    server {
        listen       80;
        server_name  test.yj-star.com;
		rewrite ^(.*)$  https://$host$1 permanent;
    }


	server {
        listen       80;
        server_name  www.tianjiaocompany.com;
        location / {
            root   html;
            index  default.aspx index.html;
            proxy_pass    http://127.0.0.1:2080;
            proxy_set_header   Host              $host;
            proxy_set_header   X-Real-IP         $remote_addr;
            proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_buffering off;
        }
        error_page   *********** 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    server {
        listen       80;
        server_name  tianjiaocompany.com;
		rewrite ^(.*)$  http://www.$host$1 permanent;
    }