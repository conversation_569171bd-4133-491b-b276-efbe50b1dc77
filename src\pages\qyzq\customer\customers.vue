<template>
  <view class="content">
    <view class="customer-list">
      <scroll-view class="list top-radius" scroll-y>
        <view class="search-box">
          <input class="search-box-input" v-model="nickName" confirm-type="search" placeholder="请输入昵称"/>
          <button @click="search">查询</button>
        </view>
        <view class="jz-list-item b-b" hover-class="item-hover" :hover-stay-time="50"
              v-for="(item, index) in pageInfo.list" :key="index" @click="login(item.openid)">
          <view class="item-title">
            <text class="ylh-icon icon-message list-icon"></text>
            {{ item.nickname }}
          </view>
          <text class="ylh-icon icon-right"></text>
        </view>
      </scroll-view>
    </view>
    <view class="jz-bottom-box">
      <view class="jz-button" @click="loginMyself()">
        <text class="ylh-icon icon-ai-safe"></text>
        返回我的账号
      </view>
    </view>
  </view>
</template>

<script>

import {getCustomers, getMyFansToken} from "@/common/request/admin";
import store from "@/store";
import {isEmpty} from "@/util/validate";
import {showMsg} from "@/common/jz-util";

export default {
  components: {},
  data() {
    return {
      nickName: '',
      pageInfo: {
        list: []
      }
    };
  },
  onLoad(options) {
  },
  methods: {
    login(openId) {
      this.$store.dispatch("user/loginByOpenId", openId).then(() => {
        this.gotoName('index')
      }).catch(res=> {
        showMsg('登录失败');
      });
      // getMyFansToken(openId).then(res => {
      //   const records = res.data.records;
      //   if (!isEmpty(records)) {
      //     const token = records[0].access_token;
      //     store.dispatch('user/setAccessToken', token);
      //     store.dispatch('user/setOtherCustomer', true);
      //     this.gotoIndex();
      //   } else {
      //     showMsg('客户长时间未登录，无法切换');
      //   }
      // }).catch(res => {
      //   console.log(res);
      // });

    },
    loginMyself() {
      store.dispatch('user/removeUserCache');
      this.gotoIndex();
    },
    search() {
      getCustomers(this.nickName).then(res => {
        let data = res.data;
        if (data && data.length > 0) {
          this.pageInfo.list = data;
        }
      }).catch(res => {
        console.log(res);
      });
    }
  }
};
</script>

<style lang="scss">
page,
.content {
  height: 100%;
  background-color: $page-color-base;
}

.content {
  padding: $page-row-spacing $page-row-spacing 0 $page-row-spacing;

  .customer-list {
    height: calc(100% - 100upx);

    .list {
      height: 100%;
    }
  }
}

.search-box {
  display: flex;
  flex-direction: row;

  input {
    flex: auto;
  }

  button {
    width: 200upx;
  }

  .search-box-input {
    height: 98upx;
    border: 2upx solid #999999;
  }
}

.jz-bottom-box {
  text-align: center;
  display: flex;

  .jz-button {
    flex: 1;
    height: 100%;
    line-height: 100rpx;
    border-radius: 0px;
    background-color: $primary-color-dark;
  }
}

</style>