import { getStore, setStore } from '@/util/store'
import store from "@/store";

let lock401 = false;

const common = {

  state: {
    authLock: false,
  },
  actions: {
    lockAuth({ state, commit }) {
      return new Promise((resolve, reject) => {
        if(state.authLock === false) {
          commit('SET_REQUEST_LOCK', true);
          setTimeout(() => {
            commit('SET_REQUEST_LOCK', false);
            console.log('release checkLogin lock');
          }, 10000);
          resolve();
        }
        else {
          reject();
        }
      })
    },
    req401() {
      return new Promise((resolve, reject) => {
        if(lock401 === true) {
          //请求将1.5秒后再执行
          setTimeout(()=>{ resolve() }, 1500);
        }
        else {
          console.log('lock401 begin')
          lock401 = true;
          //清楚缓存
          store.dispatch('user/removeUserCache');
          const route = this._vm.$Route;
          const path = { aliasPath: route.aliasPath, path: route.path, query: route.query };
          store.dispatch('user/storeNextRoute', path).then(()=> {
            store.dispatch('user/getWechatLoginUrl').then((res)=> {
              console.log('getWechatLoginUrl', res.data);
              resolve();
              window.location.href = res.data;
            }).catch((res)=>{});
          })

          // store.dispatch('user/login').then(res=> {
          //   console.log('req401 login successful')
          //   resolve();
          // }).catch(res=> { reject() });
          //防止多次跳转登录页，影响路由逻辑
          setTimeout(() => {
            lock401 = false;
            console.log('release lock');
          }, 10000);
        }
      })
    },
  },
  mutations: {
    SET_REQUEST_LOCK: (state, v) => {
      state.authLock = v
      setStore({
        name: 'requestLock',
        content: v,
        type: 'session'
      })
    },
  }
}
export default {
  namespaced: true,
  state: common.state,
  mutations: common.mutations,
  actions: common.actions
}
