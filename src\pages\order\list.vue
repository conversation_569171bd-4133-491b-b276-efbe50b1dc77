<template>
    <view class="content">
        <scroll-view class="scroll-box" scroll-y @scrolltolower="getData(true)">
            <view class="promo-list">
                <com-promo :item="item" v-for="(item, index) in pageInfo.list" :key="index"></com-promo>
            </view>
            <uni-load-more v-if="pageInfo.params.size > 0 && pageInfo.list.length > 0"
                           :status="pageInfo.loadStatus"></uni-load-more>
            <jz-empty v-if="!pageInfo.list || pageInfo.list.length === 0" content="暂无数据"></jz-empty>
        </scroll-view>
        <uni-fab :pattern="{}" horizontal="left" vertical="bottom" @fabClick="goBack"></uni-fab>
    </view>
</template>

<script>
import uniSection from '@/components/uni-section'
import uniCard from '@/components/uni-card'
import comPromo from '@/pages/home/<USER>/com-promo'
import uniFab from '@/components/uni-fab'
import {getUserGameList} from '@/common/request/user-game'
import uniLoadMore from "@/components/uni-load-more";
import {bindPageInfo, bindScrollPageInfo} from "@/common/jz-util";
import jzEmpty from '@/components/jz-empty';

export default {
    components: {uniSection, uniCard, comPromo, uniFab, uniLoadMore, jzEmpty},
    data() {
        return {
            pageInfo: {
                list: [],
                loadStatus: 'more',
                params: {
                    current: 0,
                    size: 8,
                },
            }
        }
    },
    onLoad() {
        this.getData(false)
    },
    methods: {
        getData(nextPage) {
            bindScrollPageInfo(this.pageInfo, nextPage).then(pageInfo => {
                this.pageInfo = pageInfo;
                getUserGameList(this.pageInfo.params).then(res=> {
                    const data = res.data;
                    this.pageInfo = bindPageInfo(this.pageInfo, data)
                }).catch(res=>{});
            }).catch(()=> {})
        }
    }
}
</script>

<style lang="scss">
.scroll-box {
    height: 100%;
    background-color: $page-color-base;
    padding-bottom: 20rpx;
}
.series {
    background-color: #fff;
    padding: 10rpx 24rpx 12rpx 14rpx;
    .top-title {
        font-size: $font-lg + 4rpx;
        padding-left: 12rpx;
    }
    .series-memo-box {
        display: flex;
        flex-direction: row;
        padding: 20rpx 12rpx 10rpx 20rpx;
        .series-memo-img {
            image {
                width: 160rpx;
                height: 160rpx;
            }
        }
        .series-memo-box-r {
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            .series-title-box {
                padding: 0 0 12rpx 0;
                .series-title {
                    font-size: $font-lg;
                    font-weight: 700;
                }
                .series-title-memo {
                    font-size: $font-sm;
                    padding-left: 20rpx;
                }
            }
            .series-memo {
                font-size: $font-base;
                text-indent: 2em;
                color: $font-color-light;
            }
        }
    }
}
.promo-reason {
    background-color: #fff;
    .promo-content {
        padding: 10rpx 24rpx;
        font-size: $font-lg;
        background-color: #fff;
        text-indent: 2em;
        color: $font-color-light;
    }
}
</style>