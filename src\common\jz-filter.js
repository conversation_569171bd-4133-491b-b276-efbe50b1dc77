import { config, CustomerLevel, PeriodTag } from '@/common/jz-constant';
import { isEmpty } from '@/util/validate'
import website from "@/common/website";

// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
const formatDate = function(d, fmt) {
	var o = {
		"M+": d.getMonth() + 1, //月份
		"d+": d.getDate(), //日
		"h+": d.getHours(), //小时
		"m+": d.getMinutes(), //分
		"s+": d.getSeconds(), //秒
		"q+": Math.floor((d.getMonth() + 3) / 3), //季度
		"S": d.getMilliseconds() //毫秒
	};
	if (/(y+)/.test(fmt))
		fmt = fmt.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
	for (let k in o)
		if (new RegExp("(" + k + ")").test(fmt))
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
	return fmt;
}

//数字格式化
export const number = (value, v2) => {
	if (typeof value != 'number' || value == null) { return 0; }
	let v = parseFloat(value);
	if (isNaN(v)) {
		return 0;
	}
	return Math.round(value * 100) / 100;
};
//数字格式化
export const xNumber = (value, v2) => {
	let v = number(value, v2);
	if(value > 0) {
		v = '+' + v;
	}
	return v;
};
//数字格式化
export const noPlusMinusNumber = (value, v2) => {
	let v = number(value, v2);
	if(value < 0) {
		v = 0 - v;
	}
	return v;
};

//客户级别
export const level = (value) => {
	return CustomerLevel[value] || CustomerLevel[1];
};

export const dictSF = (value) => {
	return value === true ? '是':'否';
};

export const periodTag = (value) => {
	return PeriodTag[value] || '';
}

//日期
export const date = (value, pattern, def) => {
	if(isEmpty(value)) {
		return def || '';
	}
    var d;
	var format = value.replace(/-/g, "/"); //把“-”，替换成‘/’
	if (pattern) {
        d = formatDate(new Date(format), pattern);
    }
	else {
		d = formatDate(new Date(format), "yyyy-MM-dd hh:mm:ss");
	}
    return d.toLocaleString();
}

//默认头像
export const headicon = (value, missingFile) => {
	if(typeof value == 'undefined' || value == null || value.length ==0) {
		return website.ossDomain + (missingFile || '/admin/sys-file/fb-mp/user-missing-face.png');
	} else if (value.startsWith("http")) {
		return value;
	} else {
		return website.ossDomain + value;
	}
}

//默认员工头像 emp-missing-face.png emp-missing-face-cartoon.png emp-missing-face-female.png
export const empHeadicon = (value) => {
	//临时解决方案
	//return config.qnBrandImg + 'emp-missing-face-female.png';
	//等有好图片了再切换回来
	if(typeof value == 'undefined' || value == null || value.length ==0) {
		return config.qnBrandImg + 'emp-missing-face.png';
	}
	else {
		if(value.startsWith("minio://ylhpublic.public")){
			return value.replace("minio://ylhpublic.public", config.restUrl + "/oss/view/ylhpublic");
		}
		return config.ylhImg + 'nextfs/empphoto/' + value;
	}
}

//默认员工话术
export const empMission = (value) => {
	if(typeof value == 'undefined' || value == null || value.length ==0) {
		return '让与我们相遇的每个人，遇见更美的自己';
	}else {
		return value;
	}
}

//预约状态
export const aptStatus = (value, aptImportantFlag) => {
	if(value == '已确认') {
		return '预约成功';
	}else if(value == '待确认'){
		//预约特殊标记 0、普通 1、钻石客户 2、TVIP
		if(aptImportantFlag > 0) {
			return '预约成功';
		}
	}
	return value;
}

//默认产品图片
export const productImg = (value) => {
	if(!value) {
		return website.ossDomain + '/admin/sys-file/fb-mp/picture.png'
	}else {
		return website.ossDomain + value;
	}
}

//直播间状态
export const liveStatus = (value) => {
	if(value == '101') {
		return '直播中';
	}else if(value == '102'){
		return '未开始';
	}else if(value == '103'){
		return '已结束';
	}else if(value == '104'){
		return '禁播';
	}else if(value == '105'){
		return '暂停中';
	}else if(value == '106'){
		return '异常';
	}else if(value == '107'){
		return '已过期';
	}else {
		return '未知';
	}
	return value;
}

//预约结果提示
const aptResultNoticeList = [
	'预约成功后，请合理安排到店时间，逾期后您的预约仍可保留15分钟，超过15分钟后则该预约自动失效。',
	'和朋友家人一起到店时，为保证门店预留好足够的位置，建议您发起2条预约。'
]
export const aptResultNotice = (value) => {
	let num = jzUtil.randomNum(0,1);
	return value + aptResultNoticeList[num];
}

//门店营业开始结束时间
export const shopBusinessTime = (value) => {
	if(value == null){
		return '';
	}else{
		value = value + '';
		return value.substr(0, 5);
	}
}

//门店来源(订单列表)
export const orderSubType = (value) => {
	if(value == null){
		return '';
	}else if(value =='商城' || value == '蔻匙线上'){
		return '线上';
	}
	else if(value==='兑换'){
		return value;
	}
	else {
		return '门店';
	}
}

//大额数字格式化
export const lowNumber = (value) => {
	if(value) {
		let vStr = value.toString();
		if(vStr.length > 3) {
			// 将数字转换为字符串,然后通过split方法用.分隔,取到第0个
			let numStr = vStr.split('.')[0]
			if(numStr.length<4) { // 判断数字有多长,如果小于6,,表示10万以内的数字,让其直接显示
				return numStr;
			}else if(numStr.length>=5 && numStr.length<=8){ // 如果数字大于5位,小于8位,让其数字后面加单位万
				let decimal = numStr.substring(numStr.length-4, numStr.length-4+1)
				// 由千位,百位组成的一个数字
				return parseFloat(parseInt(value / 10000)+'.'+decimal)+'万+'
			}else if(numStr.length >8){ // 如果数字大于8位,让其数字后面加单位亿
				let decimal = numStr.substring(numStr.length-8, numStr.length-8+1);
				return parseFloat(parseInt(value/100000000)+'.'+decimal)+'亿'
			}
		}
		return vStr;
	}
	return '0';
}

export const HidePhone = (phone) => {
	var result = "";
	if(!isEmpty(phone)) {
		var length = phone.length;
		if (length <= 4)
			result = phone;
		else if (length <= 8) {
			result = "****" + phone.substring(length - 4);
		} else {
			result = phone.substring(0, length - 8) + "****" + phone.substring(length - 4);
		}
	}
	return result;
}

export const ages = (input) => {
	if(isEmpty(input)) {
		return "未知";
	}
	input = new Date(input).getFullYear();
	var date = new Date();
	var year = date.getFullYear();
	if (input >= year) {
		return 0;
	} else {
		return (year - input);
	}
};

export const hideName = (name) => {
	let result = "";
	if(!isEmpty(name)) {
		result = name.substring(0, 1) + "**";
	}
	return result;
}

export const currency = (input, digit) => {
	if (isEmpty(input)) {
		return "¥" + 0;
	}
	if (!isEmpty(digit)) {
		var decimals = (input - parseInt(input)).toFixed(digit);
	}
	var money = parseInt(input);
	var res = "¥";
	var str = money.toString();
	for (var i = str.length - 1; i >= 0; i--)
	{
		res += str.charAt(str.length - 1 - i);
		if ((i) % 4 === 0 && i !== 0)
		{
			res += ",";
		}
	}
	if (!isEmpty(digit)) {
		if(decimals>0){
			res += decimals.toString().substring(1);
		}else{
			res+=".00";
		}
	}
	return res;
}

export const pvCurrency = (input, digit) => {
	if (isEmpty(input)) {
		return  0;
	}
	if (!isEmpty(digit)) {
		var decimals = (input - parseInt(input)).toFixed(digit);
	}
	return  (input/10000).toFixed(digit)+'pv';
};

/**
 * 截取字符长度
 */
export const subText = (title, length) => {
	var result = "";
	console.log('subText', isEmpty(title))
	if (isEmpty(title)) {
		return result;
	} else if (title.length <= length) {
		result = title;
	} else {
		result = title.substring(0, length) + "…";
	}
	return result;
}

const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
export const datetimeDisplay = (dateString) => {
	if(!dateString) {
		return "";
	}
	const dateValue = dateString.replace(/-/g, "/"); //把“-”，替换成‘/’
	const date = new Date(dateValue);
	const dateTime = date.getTime();
	console.log('dateTime', dateTime);
	let today = new Date();
	const now = today.getTime();
	today.setHours(0);
	today.setMinutes(0);
	today.setSeconds(0);
	today.setMilliseconds(0);
	const todayTime = today.getTime(); //今天凌晨0点0分0秒
	const yearTime = todayTime - 31536000000;	//365*24*60*60*1000
	const yesterdayTime = todayTime - 86400000; //24*60*60*1000
	const weekTime = todayTime - 604800000; //7*24*60*60*1000
	const space = now - dateTime; //当前时间与发布时间的差值
	if (space < 24 * 60 * 60 * 1000) {  //发布的时间大于今天或者发布的时间是在24小时内
		if (space < 60 * 60 * 1000) {
			var m = parseInt(space / (60 * 1000));
			if (m <= 1) {
				return "刚刚";
			} else {
				return parseInt(space / (60 * 1000)) + "分钟前";
			}

		} else {
			return parseInt(space / (60 * 60 * 1000)) + "小时前";
		}

	} else if (dateTime >= yesterdayTime)//昨天
		return "昨天";
	else if (dateTime >= weekTime)//一周内
	{
		const wk = date.getDay()
		const weekDay = weeks[wk];
		return weekDay; //+" " +angularDateFilter(dateTime, 'HH:mm');
	} else if (dateTime >= yearTime) {
		return formatDate(new Date(dateTime), 'MM-dd');
	} else {
		return formatDate(new Date(dateTime), 'yyyy/MM/dd');
	}
}

export const days = (input, year) => {
	if(typeof input === 'string'){
		input = new Date(input);
	}
	if (isEmpty(input))
		return "";
	var dd = new Date();
	dd.setDate(dd.getDate() - 1);
	var days = Math.ceil((dd.getTime() - input) / 86400000);

	if(!isEmpty(year))
	{
		if(days>365){
			return  parseInt(days/365)+"年"+days%365
		}
	}

	return days;
}

export const gameResult = (input) => {
	switch (input) {
		case 'W': return '红';
		case 'D': return '走';
		case 'L': return '黑';
	}
}
