import {showMsgConfirm} from "@/common/jz-util";
import {buyGame} from "@/common/request/order";
import {isEmpty} from "@/util/validate";

export default {
	name: 'gameMixins',
	data() {
		return {
			winTimes: '',
		};
	},
	methods: {
		getWinText(results) {
			let times = 0;
			for(let i = 0; i < results.length; i++) {
				if(this.results[i]==='W') {
					times++;
				}
			}
			if(times > 0) {
				this.winTimes = times+'连胜';
			}
		}
	}
}
