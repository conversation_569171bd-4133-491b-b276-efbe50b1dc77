
/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none; /* Chrome Safari */
}
.content {
	scrollbar-width: none; /* firefox */
	-ms-overflow-style: none; /* IE 10+ */
	font-size: $font-base;
	color: $font-color-base;
}
textarea {
	padding: 10upx 10rpx;
}
/* input 样式 */
.input-placeholder {
	color: $font-color-light;
}
.placeholder {
	color: $font-color-light;
}
/* 省略号 */
.clamp-no-m-r {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
}
.clamp {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
	margin-right: -100rpx;
}
/*边框*/
.b-b:after,
.b-t:after{
	position: absolute;
	z-index: 3;
	left: 0;
	right: 0;
	height: 0;
	content: '';
	transform: scaleY(.5);
	border-bottom: 1px solid $border-color-light;
}
//border-bottom
.b-b:after {
	bottom: 0;
}
//border-top
.b-t:after {
	top: 0;
}
.full-border {
	border: 1upx solid $border-color-light;
}
/* 圆角 */
.full-radius {
	border-radius: 12rpx;
	box-sizing: border-box;
	overflow: hidden;
}
.full-radius-circle {
	border-radius: 50%;
	box-sizing: border-box;
	overflow: hidden;
}
.top-radius {
	border-top-left-radius: 12rpx;
	border-top-right-radius: 12rpx;
	overflow: hidden;
}
.left-radius {
	border-top-left-radius: 12rpx;
	border-bottom-left-radius: 12rpx;
	overflow: hidden;
}
.right-radius {
	border-top-right-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
	overflow: hidden;
}
.bottom-radius {
	border-bottom-left-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
	overflow: hidden;
}
/* 阴影 */
.full-shadow {
	box-shadow: 0 2px 10px $border-color-light;
}
/* 边距 */
.m-t{
	margin-top: 24rpx;
}
.m-b{
	margin-bottom: 24rpx;
}
.m-r {
	margin-right: 8rpx;
}

/* 正方形图片 */
.img-wrap {
	position: relative;
	width: 100%;
	height: 0;
	padding-bottom: 100%;
	overflow: hidden;
}
.img-wrap image {
	position: absolute;
	width: 100%;
	height: 100%;
}
image {
	vertical-align: 0%;
	height: auto;
}
/* 纵向分割线 */
.lengthways-line {
	width: 2rpx;
	height: 40rpx;
	background-color: $border-color-base;
}
button {
	color: $primary-color-base;
}
button-hover {
	color: $page-color-base;
}

/* width = content + padding + border */
view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
	box-sizing: border-box;
}

image {
	vertical-align:bottom;
}

/* page */
.common-page-head{
	padding:35rpx;
	text-align: center;
}
.common-page-head-title {
	display: inline-block;
	padding: 0 40rpx;
	font-size: 30rpx;
	height: 88rpx;
	line-height: 88rpx;
	color: #BEBEBE;
	box-sizing: border-box;
	border-bottom: 2rpx solid #D8D8D8;
}

/* 重写矢量图icon css */
.icon-dianhua {
	color: $primary-color-base;
	font-size: 20px;
}

.jz-list-item {
	width: 100%;
	min-height: 100rpx;
	padding: 20rpx $page-row-spacing;
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #ffffff;
	font-size: $font-base + 2rpx;
	color: $font-color-base;
	&.item-hover {
		background: $page-color-light;
	}
	.item-title {
		flex-grow: 1;
		flex-shrink: 0;
		flex-basis: 30%;
		.ylh-icon{
			color: $primary-color-dark;
			font-size: $font-base -2rpx;
			margin-right: $page-row-spacing - 4rpx;
		}
	}
	.item-more {
		flex-grow: 1;
		text-align: right;
		color: $primary-color-dark;
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;
	}
	.icon-right{
		flex: 0;
		align-self: center;
		color: $border-color-base;
		margin-right: 8rpx-$page-row-spacing;
	}
	&.b-b:after{
		left: $page-row-spacing;
		right: $page-row-spacing;
	}
	&.b-t:after{
		left: $page-row-spacing;
		right: $page-row-spacing;
	}
	& textarea {
		width: 100%;
		min-height: 16rpx;
		line-height: 1;
	}
}



/* 串行格 */
.jz-list-cell{
	width: 100%;
	display:flex;
	justify-content: center;
	align-items: center;
	padding: 0 $page-row-spacing;
	line-height: 1;
	min-height: 90rpx;
	position:relative;
	background: #fff;
	&.log-out-btn{
		margin-top: 40rpx;
		.cell-tit{
			color: $uni-color-primary;
			text-align: center;
			margin-right: 0;
		}
	}
	&.cell-hover{
		background:#fafafa;
	}

	&.m-t{
		margin-top: 20rpx;
	}
	&.m-b{
		margin-bottom: 20rpx;
	}
	.cell-more{
		font-size:$font-base;
		color:$font-color-disabled;
		margin-left:10rpx;
		align-self: center;
		max-width: 60%;
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;
	}
	.cell-tit{
		flex: 1;
		font-size: $font-base;
		color: $font-color-dark;
		margin-right: 10rpx;
		.ylh-icon {
			font-size: $font-base;
			color: $font-color-disabled;
			margin-right: $page-row-spacing - 8rpx;
		}
	}
	.cell-tip{
		font-size: $font-base;
		color: $font-color-disabled;
	}
	switch{
		transform: translateX(16rpx) scale(.84);
	}
	.icon-right {
		font-size: $font-base - 2rpx;
		color: $font-color-disabled;
	}
}
/* 底部工具栏 */
.jz-bottom-box {
	position: absolute;
	left: 0;
	bottom: 0;
	margin: 0;
	padding: 0;
	width: 100%;
	background: #fff;
	height: $page-bottom-box-height;
	box-shadow: inset 0 2rpx 0 0 $border-color-base;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.base-choose {
		flex: 50%;
		justify-content: center;
		display: flex;
		font-size: $font-lg;
	}
	.no-choose {
		color: $font-color-disabled;
	}
	.confirm-choose {
		color: $primary-color-dark;
	}
	button {
		display: flex !important;
		justify-content: center;
		align-items: center;
		border-radius: 0 !important;
		line-height: normal !important;
		background-color: $primary-color-base;
		color: #ffffff;
		min-height: 88rpx;
		height: 100%;
	}
}
/* pick选择 */
.jz-pick {
	position: absolute;
	top: 4%;
	bottom: 4%;
	left: 6%;
	right: 6%;
	height: 92%;
	width: 88%;
	z-index: 100;
	font-size: $font-base;
	color: $font-color-base;
	border: none;
	.pick-content {
		background-color: #ffffff;
		margin-bottom: 20rpx;
		width: 100%;
		height: calc(100% - 100rpx);
		.pick-title {
			font-size: $font-base + 4rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			border-bottom: 1rpx solid $border-color-base;
		}
	}
	.pick-button {
		width: 100%;
		height: 88rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		button {
			width: 80%;
			font-size: $font-base + 2rpx;
			height: 76rpx;
			line-height: 76rpx;
			background: $primary-color-base;
			color: #ffffff;
		}
	}
	.cancel-box {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		color: $font-color-disabled;
		.ylh-icon {
			font-size: 48rpx;
		}
	}
	.back-box {
		position: absolute;
		top: 20rpx;
		left: 20rpx;
		color: $font-color-disabled;
		.ylh-icon {
			font-size: 48rpx;
		}
	}
	.jz-comments {
		margin: 0 $page-row-spacing;
	}
	.jz-bottom-box {
		position: static;
	}
}
/* 备注 */
.jz-comments {
	font-size: $font-sm + 2rpx;
	padding: 20rpx $page-row-spacing;
	line-height: 1.5;
	background-color: $page-color-light;
	color: $primary-error-base;
}
/* 卡片 */
.jz-card-box {
	.box-top {
		width: 100%;
		min-height: 280rpx;
		background-image: url('https://ylhmedia.yilihui.com/ylh/mini/card_top.png');/* 引入图片 */
		background-size: 100% 100%;
		background-repeat: no-repeat;/* 如果图片比较小,框比他大的时候,设置的显示方式,repeat-x沿x轴显示,repeat-y沿y轴显示 */
		background-position: left top;/* 设置图片的位置,left top左上,center center居中..... */
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: center;
		overflow: hidden;
		padding: 20rpx $page-row-spacing*2;
	}
	.box-bottom {
		width: 100%;
		// height: 480rpx;
		background-image: url('https://ylhmedia.yilihui.com/ylh/mini/card_bottom.png');/* 引入图片 */
		background-size: 100% 100%;
		background-repeat: no-repeat;/* 如果图片比较小,框比他大的时候,设置的显示方式,repeat-x沿x轴显示,repeat-y沿y轴显示 */
		background-position: left top;/* 设置图片的位置,left top左上,center center居中..... */
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		overflow: hidden;
		padding: 0 $page-row-spacing*2 20rpx $page-row-spacing*2;
	}
	.title {
		width: 100%;
		min-height: 88rpx;
		font-size: $font-lg + 8rpx;
		color: $primary-color-dark;
		font-weight: bold;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.line{
		width: 100%;
		min-height: 100rpx;
		font-size: $font-lg;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.column{
			width: 40%;
			color: $font-color-disabled;
		}
		.value{
			width: 60%;
			color: $font-color-base;
			display: flex;
			justify-content: flex-end;
		}
	}
	.textarea-line{
		color: $font-color-base;
		position: relative;
		.textarea-box {
			background-color: $page-color-light;
			padding: 10rpx 10rpx 80rpx 10rpx;
			textarea {
				margin-top: 12rpx;
				background-color: $page-color-light;
				min-height: 150rpx;
			}
		}
		.comments-submit {
			position: absolute;
			bottom: 8rpx;
			right: 8rpx;
			z-index: 100;
			font-size: $font-base + 4rpx;
			color: $primary-color-dark;
			.ylh-icon {
				color: $primary-color-dark;
			}
		}
	}
	.button-line {
		width: 100%;
		min-height: 100rpx;
		margin: 20rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
		.cancel{
			background-color: #ffffff !important;
			color: $font-color-disabled !important;
			border: 1px solid #D1D1D1;
		}
		button {
			height: 80rpx;
			line-height: 80rpx;
			width: 40%;
			background-color: $primary-color-base;
			color: #ffffff;
		}
	}

}
.jz-textarea-box {
	background-color: #ffffff;
	min-height: 92rpx;
	padding: 20rpx $page-row-spacing;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	& .ylh-icon{
		color: $font-color-disabled;
		font-size: $font-base -2rpx;
		margin-right: $page-row-spacing - 6rpx;
	}
	.jz-textarea {
		font-size: $font-base;
		color: $font-color-base;
	}
	.jz-input {
		width: 100%;
		font-size: $font-base;
		color: $font-color-base;
	}
}

.jz-alink {
	text-decoration: underline;
	color: $primary-color-base;
}

.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.jz-banner {
	//padding: 0 $page-row-spacing * 2;
	//background-image: url('https://ylhmedia.yilihui.com/ylh/mini/mall-bg.png'); /* 引入图片 */
	background-size: 100% 100%;
	background-repeat: no-repeat; /* 如果图片比较小,框比他大的时候,设置的显示方式,repeat-x沿x轴显示,repeat-y沿y轴显示 */
	background-position: left top; /* 设置图片的位置,left top左上,center center居中..... */
	display: flex;
	justify-content: space-between;
	image{
		width: 100%;
	}
}
.jz-box {
	padding: 0 $page-row-spacing;
}

.jz-page-header {
	position: sticky;
	width: 100%;
	top: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	z-index: 1;
}

.jz-button {
	background-color: $primary-color-base;
	color: #ffffff;
	width: 100%;
	font-size: $font-base;
	border-radius: 8rpx;
	text-align: center;
}

.jz-view-button {
	background-color: $primary-color-base;
	color: #ffffff;
	font-size: $font-base - 2rpx;
	padding: 16rpx 28rpx;
	border-radius: 8rpx;
	text-align: center;
}

.jz-label {
	color: $primary-color-base;
	font-size: $font-base - 4rpx;
	padding: 6rpx 28rpx;
	border-radius: 8rpx;
	border: 1px solid $primary-color-base;
	text-align: center;
}
