<template>
  <view class="content">
    <view class="user-info">
      <view class="jz-list-item">
        <view class="left">
          <view class="img-wrap full-radius">
            <image :src="accountFans.headimgUrl | headicon" mode="widthFix"></image>
          </view>
        </view>
        <view class="right">
          <view class="top" style="align-items: flex-start;height: 70rpx;">
            <text class="name" style="font-size: 34rpx;" @click="clickUserInfo()">
              {{ accountFans && accountFans.id ? accountFans.nickname : '点击登录' }}
            </text>
          </view>
          <view class="bottom">
            <view class="level full-radius">
              金币：{{ wallet.coin | number(2) }}
            </view>
            <view class="jz-view-button charge" @click="openPopup()">充&nbsp;值</view>
          </view>
        </view>
      </view>
    </view>
    <view class="cust-content">
      <view class="jz-list-item top-radius b-t" hover-class="item-hover" :hover-stay-time="50"
            @click="goto('/order/list')">
        <view class="item-title">
          <text class="ylh-icon icon-zhibo list-icon"></text>
          我的方案
        </view>
        <text class="ylh-icon icon-right"></text>
      </view>
      <view class="jz-list-item" hover-class="item-hover" :hover-stay-time="50" @click="goto('/customer/balance/list')">
        <view class="item-title">
          <text class="ylh-icon icon-wodefensi list-icon"></text>
          我的流水
        </view>
        <text class="ylh-icon icon-right"></text>
      </view>
      <view class="jz-list-item bottom-radius b-b" hover-class="item-hover" :hover-stay-time="50"
            @click="goto('/customer/contact')">
        <view class="item-title">
          <text class="ylh-icon icon-wodefensi list-icon"></text>
          联系客服
        </view>
        <text class="ylh-icon icon-right"></text>
      </view>
      <!--            <view class="jz-list-item bottom-radius b-b" hover-class="item-hover" :hover-stay-time="50"-->
      <!--                  @click="goto('/game/add')">-->
      <!--                <view class="item-title">-->
      <!--                    <text class="ylh-icon icon-wodefensi list-icon"></text>-->
      <!--                    新增赛事-->
      <!--                </view>-->
      <!--                <text class="ylh-icon icon-right"></text>-->
      <!--            </view>-->
    </view>
    <charge ref="popup"></charge>
  </view>
</template>

<script>
import {mapState, mapMutations} from 'vuex';
import charge from "./components/charge";

export default {
  components: {charge},
  computed: {
    ...mapState({
      userInfo: state => {
        return state.user.userInfo
      },
    })
  },
  data() {
    return {
      wallet: {},
      accountFans: {},
      chargeCoinQty: 100,
    }
  },
  onShow() {
    this.getData();
  },
  methods: {
    getData() {
      this.$store.dispatch('user/getWallet').then((wallet) => {
        this.wallet = wallet;
      }).catch(res => {
      });
      this.$store.dispatch('user/getAccountFans').then((data) => {
        this.accountFans = data;
      }).catch(res => {
      });

    },
    openPopup() {
      this.$refs.popup.openPopup()
    }
  }
}
</script>

<style lang="scss">
.user-info {
  background-color: #ffffff;
  background-image: url('https://oss.yj-star.com/admin/sys-file/fb-mg/customer-banner.png');
  background-size: 100% 100%;
  background-repeat: no-repeat; /* 如果图片比较小,框比他大的时候,设置的显示方式,repeat-x沿x轴显示,repeat-y沿y轴显示 */
  background-position: left top; /* 设置图片的位置,left top左上,center center居中..... */
  .list {
    height: 100%;
  }

  .jz-list-item {
    background-color: rgba(255, 255, 255, 0.1);
    align-items: flex-start;
    padding: 24rpx $page-row-spacing;

    .left {
      flex: 0 0 18%;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }

    .right {
      flex-grow: 1;
      margin-left: $page-row-spacing - 10rpx;

      .top {
        height: 48rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        color: #ffffff;

        .name {
          font-size: $font-base + 4rpx;
          font-weight: bold;
        }
      }

      .bottom {
        font-size: $font-base;
        color: #ffffff;
        display: flex;
        justify-content: flex-start;

        .level {
          padding: 4rpx $page-row-spacing/2;
          border: 1rpx solid #ffffff
        }

        .charge {
          margin-left: 12rpx;
          padding: 8rpx 28rpx;
          background-color: orange;
          font-weight: bold;
        }
      }
    }
  }
}

.cust-content {
  padding: 24rpx 24rpx 2rpx 24rpx;

  .item-title {
    align-items: center;
    height: 100%;
    display: flex;

    .list-icon {
      font-size: 50rpx;
    }

    .icon-zhibo {
      transform: scale(0.7);
    }
  }

  button {
    line-height: 55rpx;
    border: none;
    border-radius: 0;
  }

  button:after {
    border: none;
  }
}
</style>
