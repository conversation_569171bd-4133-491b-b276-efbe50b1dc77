{"name": "", "appid": "", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "mp-qq": {"usingComponents": true}, "h5": {"router": {"base": "/#wechat#/wx65238bccf6625005/", "mode": "history"}, "publicPath": "/#wechat#/wx65238bccf6625005/", "devServer": {"https": false, "port": 8088, "disableHostCheck": true, "proxy": {"/admin": {"target": "http://cloud-gateway:9999", "changeOrigin": true, "secure": true}, "/football": {"target": "http://cloud-gateway:9999", "changeOrigin": true, "secure": true}, "/auth": {"target": "http://cloud-gateway:9999", "changeOrigin": true, "secure": true}}}, "domain": "https://eafb7dit2brujc4jqx5qr6kwjofxgrvwymjppfcmb1tnamspdjuimcyftvv3d6o.qyqy268.com/#wechat#/wx65238bccf6625005", "optimization": {"treeShaking": {"enable": true}}}}