server {
	listen       443 http2 ssl;
	server_name  pay.yj-star.com ooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo.yj-star.com;
        ssl_certificate      yj-star.com.cer;
        ssl_certificate_key  yj-star.com.key;

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;
	#charset koi8-r;

	#access_log  logs/mastertest.log  main;
	
	gzip on;
	gzip_buffers 32 4K;
	gzip_comp_level 6;
	gzip_min_length 100;
	gzip_types application/javascript text/css text/xml;
	gzip_disable "MSIE [1-6]\."; #配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
	gzip_vary on;
	
	# 避免端点安全问题
	if ($request_uri ~ "/actuator"){
		return 403;
	}
	
	location ~* ^/wechat/ {
		proxy_set_header Host $http_host;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_connect_timeout 120s;
		proxy_send_timeout 120s;
		proxy_read_timeout 120s;
		proxy_pass http://**********:8088;
		#try_files $uri $uri/ /wechat/wx7125bd357001d576/index.html;
	}
	
	location ~* ^/football-test/ws/ {
		#proxy_pass http://stream_pay_yjstar;
		proxy_pass http://**********:9999;
		proxy_set_header Host $http_host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_connect_timeout 120s;
		proxy_send_timeout 120s;
		proxy_read_timeout 3600s;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection "upgrade";
	}

	#location / {
	location ~* ^/(theater|theater1|theater-test|football|football-test|h5|code|auth|admin|gen|daemon|tx|act|monitor|mp|job|pay) {
		#proxy_next_upstream  error timeout invalid_header http_404 http_500 http_502 http_503 http_504 non_idempotent;
		#proxy_pass http://stream_pay_yjstar;
		proxy_pass http://**********:9999;
		proxy_set_header Host $http_host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_connect_timeout 120s;
		proxy_send_timeout 120s;
		proxy_read_timeout 120s;
	}
	
	location ~* ^/mgr/ {
		root /home/<USER>/A/;
		index  index.html index.htm;
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header  Host $http_host;
		proxy_read_timeout 300;
		try_files /mgr/$uri $uri /mgr/index.html;
	}
	
	location / {
		proxy_set_header Host $http_host;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_read_timeout 3600s;
		proxy_connect_timeout 120s;
		proxy_send_timeout 120s;
		proxy_pass http://**********:8080;
		#try_files $uri $uri/ /wechat/wx7125bd357001d576/index.html;
	}
}

server {
	listen       80;
	server_name  p.yj-star.com;

	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	location / {
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		#proxy_set_header Connection "Keep-Alive";
		proxy_set_header  Host $http_host;
		proxy_redirect off;
		proxy_read_timeout 300;
		proxy_pass http://127.0.0.1:5003/;
	}
	#error_page   500 502 503 504  /50x.html;
	#location = /50x.html {
	#    root   html;
	#}
}

server {
	listen       443 ssl;
	server_name  xp.yj-star.com;
	ssl_certificate      xp.yj-star.com.pem;
	ssl_certificate_key  xp.yj-star.com.key;

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;
	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	location / {
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		#proxy_set_header Connection "Keep-Alive";
		proxy_set_header  Host $http_host;
		proxy_redirect off;
		proxy_read_timeout 300;
		proxy_pass http://127.0.0.1:2080;
	}
	#error_page   500 502 503 504  /50x.html;
	#location = /50x.html {
	#    root   html;
	#}
}

server {
	listen       80;
	server_name  xp.yj-star.com;

	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	location / {
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		#proxy_set_header Connection "Keep-Alive";
		proxy_set_header  Host $http_host;
		proxy_redirect off;
		proxy_read_timeout 300;
		proxy_pass http://127.0.0.1:2080;
	}
	#error_page   500 502 503 504  /50x.html;
	#location = /50x.html {
	#    root   html;
	#}
}

server {
	listen       443 ssl;
	server_name  oss.yj-star.com;
	ssl_certificate      yj-star.com.cer;
	ssl_certificate_key  yj-star.com.key;

	ssl_session_cache    shared:SSL:1m;
	ssl_session_timeout  5m;

	ssl_ciphers  HIGH:!aNULL:!MD5;
	ssl_prefer_server_ciphers  on;
	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	# 避免端点安全问题
	if ($request_uri ~ "/actuator"){
		return 403;
	}
	
	#连接数查看
	#location /status {
	#   stub_status on;
	#}
		
	 location ~ .*\.(png|jpg|jpeg|gif)$ {
	 	proxy_next_upstream  error timeout invalid_header http_500 http_502 http_503 http_504 non_idempotent;
	 	proxy_set_header  X-Real-IP  $remote_addr;
	 	proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
	 	proxy_set_header Connection "Keep-Alive";
	 	proxy_set_header  Host $http_host;
	 	proxy_redirect off;
	 	proxy_read_timeout 300;
	 	proxy_pass http://stream_oss;
	 	#忽略浏览器的缓存
	 	proxy_ignore_headers Cache-Control;
	 	proxy_ignore_headers Expires;
	 	proxy_cache cache_two;
	 	proxy_cache_valid 200 302 1d;
	 	proxy_cache_valid 404 1h;
	 	proxy_cache_valid any 10m;
	 	proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
	 }
		
	location / {
		proxy_next_upstream  error timeout invalid_header http_500 http_502 http_503 http_504 non_idempotent;
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		#proxy_set_header Connection "Keep-Alive";
		proxy_set_header  Host $http_host;
		proxy_redirect off;
		proxy_read_timeout 300;
		proxy_pass http://stream_oss;

	}
	#error_page   500 502 503 504  /50x.html;
	#location = /50x.html {
	#    root   html;
	#}
}

server {
	listen       80;
	server_name  oss.yj-star.com;

	#charset koi8-r;

	#access_log  logs/mastertest.log  main;

	# 避免端点安全问题
	if ($request_uri ~ "/actuator"){
		return 403;
	}
	location / {
		proxy_set_header  X-Real-IP  $remote_addr;
		proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
		#proxy_set_header Connection "Keep-Alive";
		proxy_set_header  Host $http_host;
		proxy_redirect off;
		proxy_read_timeout 300;
		#proxy_pass http://127.0.0.1:5003/;
		proxy_pass http://stream_oss;
	}
	#error_page   500 502 503 504  /50x.html;
	#location = /50x.html {
	#    root   html;
	#}
}
