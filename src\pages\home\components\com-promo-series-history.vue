<template>
    <view class="series-result-box">
        <block v-for="item in items">
            <view class="game-result-icon" :class="resultClass(item.result)">{{item.result|gameResult}}</view>
        </block>
    </view>
</template>

<script>

import {getGameResultList} from "@/common/request/promo-series";

export default {
    name: "comPromoSeriesHistory",
    props: {
        seriesId: ''
    },
    data() {
        return {
            items: []
        }
    },
    mounted() {
        console.log('mounted')
        if(this.seriesId) {
            this.getGameResultList();
        }
    },
    methods: {
        resultClass(result) {
            switch (result) {
                default:
                case 'D': return 'color-green';
                case 'W': return 'color-red';
                case 'L': return 'color-black';
            }
        },
        getGameResultList() {
            getGameResultList(this.seriesId).then(res=> {
                this.items = res.data;
            }).catch(res=> {})
        }
    }
}
</script>

<style lang="scss" scoped>
.series-result-box {
    padding: 12rpx 28rpx 28rpx 28rpx;
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
}
.game-result-icon {
    display:flex;
    justify-content:center;
    border-radius:50%;
    width:50rpx;
    height:50rpx;
    line-height:44rpx;
    font-size: 28rpx;
    font-weight: 600;
    margin-left: 20rpx;
    margin-top: 20rpx;
}
.color-red {
    color:red;
    border:2rpx solid red;
}
.color-black {
    color:#000;
    border:2rpx solid #000;
}
.color-green {
    color: #4cb84b;
    border:2rpx solid #4cb84b;
}
</style>
